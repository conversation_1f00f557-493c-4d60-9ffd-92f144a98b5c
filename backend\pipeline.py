# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Created by: jas<PERSON><PERSON>
# Created on: 2022-11-11
# Email: zhu<PERSON><PERSON><PERSON><EMAIL>
#
# Copyright © 2022 - CPSS Group
# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import os
import io
import numpy as np
import yaml
import argparse
from argparse import Namespace
from tqdm import tqdm
import warnings
import matplotlib.pyplot as plt
import seaborn as sns
from transformers import AutoTokenizer
import pandas as pd
warnings.filterwarnings("ignore")
import torch
import torch.nn.functional as F
from models.factory import create_model
from lib.metrics import *
plt.rcParams.update({'font.size': 30})
from Bio import SeqIO
torch.backends.cudnn.benchmark = True
# 替换硬编码路径
# path = '/tmp/pycharm_project_521/DNAbert2_attention'
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
path = os.path.join(BASE_DIR, 'DNAbert2_attention')

class Pipeline(object):
    def __init__(self, cfg):
        super(Pipeline, self).__init__()

        # 设备检测：检查是否强制使用CPU
        force_cpu = getattr(cfg, 'force_cpu', False)
        if force_cpu:
            self.device = torch.device('cpu')
            print('强制使用CPU进行预测')
        else:
            # 使用具体的GPU设备号，避免cuda vs cuda:0的不匹配
            if torch.cuda.is_available():
                self.device = torch.device('cuda:0')
                print(f'使用GPU进行预测: {torch.cuda.get_device_name(0)}')
            else:
                self.device = torch.device('cpu')
                print('GPU不可用，使用CPU进行预测')

        # 在确定设备后创建模型
        # 如果使用CPU，临时禁用CUDA来创建模型
        original_cuda_available = torch.cuda.is_available
        if self.device.type == 'cpu':
            # 临时禁用CUDA
            torch.cuda.is_available = lambda: False

        try:
            self.model = create_model(cfg.model, cfg=cfg)
            # 立即将模型移动到CPU，避免任何GPU初始化
            self.model = self.model.cpu()
            print(f"模型创建完成，初始设备: {next(self.model.parameters()).device}")
        finally:
            # 恢复原始的CUDA检测函数
            if self.device.type == 'cpu':
                torch.cuda.is_available = original_cuda_available

        self.cfg = cfg
        self.tokenizer = AutoTokenizer.from_pretrained(path, trust_remote_code=True)
        self.labels = [line.strip() for line in open(cfg.label_path)]

    def _ensure_model_on_device(self):
        """确保模型及其所有组件都在正确的设备上"""
        print(f"开始将模型移动到设备: {self.device}")

        # 如果使用CPU，先清理GPU缓存并禁用CUDA
        if self.device.type == 'cpu':
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                print("已清理GPU缓存")

        # 递归地将所有子模块移动到指定设备
        def move_module_to_device(module, device):
            """递归移动模块到指定设备"""
            module.to(device)
            for child in module.children():
                move_module_to_device(child, device)

            # 确保所有参数都在正确设备上
            for param in module.parameters(recurse=False):
                if param.device != device:
                    param.data = param.data.to(device)
                    if param.grad is not None:
                        param.grad.data = param.grad.data.to(device)

            # 确保所有缓冲区都在正确设备上
            for buffer in module.buffers(recurse=False):
                if buffer.device != device:
                    buffer.data = buffer.data.to(device)

        # 应用递归移动
        move_module_to_device(self.model, self.device)

        # 智能设备验证：检查设备类型而不是具体设备号
        def devices_match(device1, device2):
            """检查两个设备是否匹配（忽略具体的GPU编号）"""
            if device1.type != device2.type:
                return False
            if device1.type == 'cuda' and device2.type == 'cuda':
                # 对于CUDA设备，只要都是CUDA就认为匹配
                return True
            return device1 == device2

        # 最终验证
        device_mismatches = []
        for name, param in self.model.named_parameters():
            if not devices_match(param.device, self.device):
                device_mismatches.append(f"{name}: {param.device}")

        for name, buffer in self.model.named_buffers():
            if not devices_match(buffer.device, self.device):
                device_mismatches.append(f"{name} (buffer): {buffer.device}")

        if device_mismatches:
            print(f"警告: 发现设备类型不匹配的组件:")
            for mismatch in device_mismatches[:3]:  # 只显示前3个
                print(f"  - {mismatch}")
            if len(device_mismatches) > 3:
                print(f"  ... 还有 {len(device_mismatches) - 3} 个不匹配的组件")
        else:
            print(f"✅ 所有模型组件都已成功移动到 {self.device.type}")

        # 验证第一个参数的设备
        try:
            first_param_device = next(self.model.parameters()).device
            print(f"模型设备验证: {first_param_device}")
        except StopIteration:
            print('警告: 模型没有参数')

    @torch.no_grad()
    def run(self):
        """原始的运行方法，从文件读取FASTA序列"""
        # 加载模型权重
        model_dict = torch.load(self.cfg.ckpt_best_path, map_location='cpu')
        # print(model_dict.keys())
        if list(model_dict.keys())[0].startswith('module'):
            model_dict = {k[7:]: v for k, v in model_dict.items()}

        # 加载权重
        self.model.load_state_dict(model_dict)

        # 确保模型在正确的设备上
        self._ensure_model_on_device()

        self.model.eval()
        print(f'loading best checkpoint success, using device: {self.device}')
        ids = []
        all_y_score = []
        for record in SeqIO.parse(self.cfg.input_path, "fasta"):
            description = record.description
            ids.append(description[1:])
            seq = str(record.seq)[:5000]
            print(len(seq))
            if seq:
                inputs = self.tokenizer(
                    seq,
                    add_special_tokens=True,
                    return_token_type_ids=True,
                    return_attention_mask=True,
                    return_tensors='pt'
                )
                # 推断（预测）
                with torch.no_grad():
                    self.model.eval()
                    outputs = self.model(
                        inputs["input_ids"].to(self.device),
                        inputs["attention_mask"].to(self.device),
                        inputs["token_type_ids"].to(self.device)
                    )
                    scores = torch.sigmoid(outputs['logits']).cpu().numpy()[0]
                    all_y_score.append(scores)

            all_y_score = np.array(all_y_score)
            all_y_pred = np.where(all_y_score > 0.5, 1, 0)
            output_path = "output_result"

            if not os.path.isdir(output_path):
                os.makedirs(output_path)
            pd.DataFrame(
                all_y_score,
                columns = ['Exosome','Nucleus','Nucleoplasm','Chromatin','Nucleolus','Cytosol','Membrane','Ribosome','Cytoplasm'],
                index=ids,
            ).to_csv(os.path.join(output_path, "score.csv"))
            pd.DataFrame(
                all_y_pred,
                columns = ['Exosome','Nucleus','Nucleoplasm','Chromatin','Nucleolus','Cytosol','Membrane','Ribosome','Cytoplasm'],
                index=ids,
            ).to_csv(os.path.join(output_path, "pred.csv"))
            print('output result success')

    @torch.no_grad()
    def predict_from_string(self, fasta_string):
        """
        从FASTA格式字符串进行预测

        Args:
            fasta_string (str): FASTA格式的RNA序列字符串

        Returns:
            list: 包含每条序列预测结果的列表，每个元素是一个字典
        """
        # 加载模型（如果还没有加载）
        if not hasattr(self, '_model_loaded'):
            # 加载模型权重到CPU，然后移动到目标设备
            model_dict = torch.load(self.cfg.ckpt_best_path, map_location='cpu')
            if list(model_dict.keys())[0].startswith('module'):
                model_dict = {k[7:]: v for k, v in model_dict.items()}

            # 加载权重
            self.model.load_state_dict(model_dict)

            # 确保模型在正确的设备上
            self._ensure_model_on_device()

            self.model.eval()
            self._model_loaded = True
            print(f'模型加载成功，使用设备: {self.device}')

        # 解析FASTA字符串
        fasta_io = io.StringIO(fasta_string)
        results = []

        # 亚细胞定位标签
        location_labels = ['Exosome','Nucleus','Nucleoplasm','Chromatin','Nucleolus','Cytosol','Membrane','Ribosome','Cytoplasm']

        for record in SeqIO.parse(fasta_io, "fasta"):
            sequence_id = record.description
            seq = str(record.seq)[:5000]  # 限制序列长度为5000

            if seq:
                # 对序列进行tokenization
                inputs = self.tokenizer(
                    seq,
                    add_special_tokens=True,
                    return_token_type_ids=True,
                    return_attention_mask=True,
                    return_tensors='pt'
                )

                # 进行预测
                with torch.no_grad():
                    # 确保输入数据在正确设备上
                    input_ids = inputs["input_ids"].to(self.device)
                    attention_mask = inputs["attention_mask"].to(self.device)
                    token_type_ids = inputs["token_type_ids"].to(self.device)

                    # 只在第一次预测时验证设备（减少日志输出）
                    if not hasattr(self, '_device_verified'):
                        model_device = next(self.model.parameters()).device
                        if model_device.type != self.device.type:
                            print(f"警告: 模型设备({model_device})与目标设备({self.device})类型不匹配")
                        self._device_verified = True

                    outputs = self.model(input_ids, attention_mask, token_type_ids)

                    # 计算sigmoid概率分数
                    scores = torch.sigmoid(outputs['logits']).cpu().numpy()[0]
                    # 根据阈值0.5进行二分类预测
                    predictions = np.where(scores > 0.5, 1, 0)

                # 构建结果字典
                result = {
                    'sequence_id': sequence_id,
                    'sequence_length': len(seq),
                    'predictions': {},
                    'scores': {}
                }

                # 添加每个亚细胞定位的预测结果和分数
                for i, label in enumerate(location_labels):
                    result['predictions'][label] = int(predictions[i])
                    result['scores'][label] = float(scores[i])

                # 找到预测为1的位置（可能有多个）
                predicted_locations = [label for i, label in enumerate(location_labels) if predictions[i] == 1]
                result['predicted_locations'] = predicted_locations

                # 如果没有预测为1的位置，选择分数最高的位置
                if not predicted_locations:
                    max_score_idx = np.argmax(scores)
                    result['predicted_locations'] = [location_labels[max_score_idx]]

                results.append(result)

        return results

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--exp-dir', type=str, default='./exp10/')
    parser.add_argument('--threshold', type=float, default=0.5)
    parser.add_argument('--input-path', type=str, default='./AC006160.fasta')

    args = parser.parse_args()
    cfg_path = os.path.join(args.exp_dir, 'config.yaml')
    if not os.path.exists(cfg_path):
        raise FileNotFoundError('config file not found in the {}!'.format(cfg_path))
    cfg = yaml.load(open(cfg_path, 'r'),Loader=yaml.FullLoader)
    cfg = Namespace(**cfg)
    cfg.ckpt_best_path = os.path.join(args.exp_dir, 'checkpoints','best_model.pth')
    cfg.threshold = args.threshold
    cfg.input_path = args.input_path
    evaluator = Pipeline(cfg)
    evaluator.run()
