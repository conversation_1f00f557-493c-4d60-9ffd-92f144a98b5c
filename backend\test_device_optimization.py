#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设备优化效果的脚本
"""

import torch

def test_device_matching():
    """测试设备匹配逻辑"""
    print("=" * 50)
    print("设备匹配逻辑测试")
    print("=" * 50)
    
    def devices_match(device1, device2):
        """检查两个设备是否匹配（忽略具体的GPU编号）"""
        if device1.type != device2.type:
            return False
        if device1.type == 'cuda' and device2.type == 'cuda':
            # 对于CUDA设备，只要都是CUDA就认为匹配
            return True
        return device1 == device2
    
    # 测试用例
    test_cases = [
        (torch.device('cpu'), torch.device('cpu')),
        (torch.device('cuda'), torch.device('cuda:0')),
        (torch.device('cuda:0'), torch.device('cuda')),
        (torch.device('cuda:0'), torch.device('cuda:1')),
        (torch.device('cpu'), torch.device('cuda')),
    ]
    
    for device1, device2 in test_cases:
        match = devices_match(device1, device2)
        print(f"{device1} vs {device2}: {'✅ 匹配' if match else '❌ 不匹配'}")
    
    print("\n" + "=" * 50)

def test_device_selection():
    """测试设备选择逻辑"""
    print("设备选择逻辑测试")
    print("=" * 50)
    
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    # 模拟不同的设备选择
    if torch.cuda.is_available():
        device_auto = torch.device('cuda:0')
        print(f"自动选择GPU: {device_auto}")
    else:
        device_auto = torch.device('cpu')
        print(f"自动选择CPU: {device_auto}")
    
    device_cpu = torch.device('cpu')
    print(f"强制CPU: {device_cpu}")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    test_device_matching()
    test_device_selection()
    print("测试完成!")
