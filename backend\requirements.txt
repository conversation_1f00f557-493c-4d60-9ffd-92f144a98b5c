# RNA亚细胞定位预测项目依赖 - CPU版本
# 适用于Docker部署和无GPU环境
--index-url https://pypi.tuna.tsinghua.edu.cn/simple
--extra-index-url https://download.pytorch.org/whl/cpu

# Web框架
Flask==2.3.3
Flask-CORS==3.0.10

# 深度学习框架 (CPU版本)
# 使用PyTorch CPU版本的直接URL
torch==2.1.0+cpu
torchvision==0.16.0+cpu
torchaudio==2.1.0+cpu

# Transformers和相关模型
transformers==4.41.2
multimolecule==0.0.3

# 数据处理
numpy==1.26.3
pandas==2.2.2
PyYAML==6.0.1

# 生物信息学
biopython==1.83

# 机器学习
scikit-learn==1.5.0
scipy==1.13.1

# 可视化
matplotlib==3.9.0
seaborn==0.13.2

# 工具库
tqdm==4.67.1
einops==0.8.0

# 必需：multimolecule的依赖
accelerate==0.24.1
