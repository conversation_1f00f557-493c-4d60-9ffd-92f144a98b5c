{"version": "1.0", "truncation": null, "padding": null, "added_tokens": [{"id": 0, "special": true, "content": "[UNK]", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false}, {"id": 1, "special": true, "content": "[CLS]", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false}, {"id": 2, "special": true, "content": "[SEP]", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false}, {"id": 3, "special": true, "content": "[PAD]", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false}, {"id": 4, "special": true, "content": "[MASK]", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false}], "normalizer": null, "pre_tokenizer": {"type": "Whitespace"}, "post_processor": {"type": "TemplateProcessing", "single": [{"SpecialToken": {"id": "[CLS]", "type_id": 0}}, {"Sequence": {"id": "A", "type_id": 0}}, {"SpecialToken": {"id": "[SEP]", "type_id": 0}}], "pair": [{"SpecialToken": {"id": "[CLS]", "type_id": 0}}, {"Sequence": {"id": "A", "type_id": 0}}, {"SpecialToken": {"id": "[SEP]", "type_id": 0}}, {"Sequence": {"id": "B", "type_id": 1}}, {"SpecialToken": {"id": "[SEP]", "type_id": 1}}], "special_tokens": {"[CLS]": {"id": "[CLS]", "ids": [1], "tokens": ["[CLS]"]}, "[SEP]": {"id": "[SEP]", "ids": [2], "tokens": ["[SEP]"]}}}, "decoder": null, "model": {"type": "BPE", "dropout": null, "unk_token": "[UNK]", "continuing_subword_prefix": null, "end_of_word_suffix": null, "fuse_unk": false, "vocab": {"[UNK]": 0, "[CLS]": 1, "[SEP]": 2, "[PAD]": 3, "[MASK]": 4, "A": 5, "C": 6, "G": 7, "T": 8, "AA": 9, "TT": 10, "TG": 11, "CA": 12, "CC": 13, "TA": 14, "GG": 15, "TC": 16, "GA": 17, "AAA": 18, "GC": 19, "TAA": 20, "TTTT": 21, "TCA": 22, "TGA": 23, "TTA": 24, "GAA": 25, "TCC": 26, "CAA": 27, "CTG": 28, "CTT": 29, "GTG": 30, "GTT": 31, "GCA": 32, "GGA": 33, "CCA": 34, "GTA": 35, "GCC": 36, "CTA": 37, "TAAA": 38, "AAAA": 39, "CTC": 40, "GTC": 41, "TGTG": 42, "TATT": 43, "CACA": 44, "GAAA": 45, "TATA": 46, "TCTT": 47, "TGTT": 48, "CAAA": 49, "GAGA": 50, "CATT": 51, "TGAA": 52, "CAGG": 53, "TCTG": 54, "CAGA": 55, "TCAA": 56, "GGAA": 57, "TAAAA": 58, "CTGA": 59, "GCTT": 60, "GTGA": 61, "GCTG": 62, "CTCA": 63, "CCTT": 64, "CATG": 65, "GCAA": 66, "GTCA": 67, "GTAA": 68, "TTTTA": 69, "TATG": 70, "GAGG": 71, "CGG": 72, "GATT": 73, "CCTG": 74, "TCTC": 75, "CCAA": 76, "GTTA": 77, "CTCC": 78, "CTAA": 79, "TACA": 80, "CTTA": 81, "TCCA": 82, "GATG": 83, "TTAA": 84, "GAAAA": 85, "TTTG": 86, "GTTTT": 87, "TCTA": 88, "GCCA": 89, "GTCC": 90, "CTTTT": 91, "GGGG": 92, "CGA": 93, "TTTA": 94, "CCCA": 95, "CAAAA": 96, "TGGG": 97, "TAGA": 98, "TAGG": 99, "GACA": 100, "GGTT": 101, "CCCC": 102, "GGTG": 103, "CATA": 104, "GCTA": 105, "TGTA": 106, "TCAAA": 107, "TGGA": 108, "TAATT": 109, "TTATT": 110, "TGCA": 111, "GGCA": 112, "GATA": 113, "CCTA": 114, "TTCA": 115, "TCTCA": 116, "GGGA": 117, "CGC": 118, "CTGAA": 119, "GTAAA": 120, "TCTCC": 121, "TTTTTT": 122, "CGTG": 123, "GCAAA": 124, "TAAAAA": 125, "TCTGA": 126, "TCATT": 127, "GGAAA": 128, "TGAAA": 129, "TCCTT": 130, "CCAAA": 131, "GAATT": 132, "CTAAA": 133, "CGTT": 134, "GTGAA": 135, "GGCC": 136, "TAATA": 137, "GGTA": 138, "TGCC": 139, "CACC": 140, "TGATT": 141, "AAAAAA": 142, "GCTCA": 143, "TCCAA": 144, "GAGAA": 145, "CTGTT": 146, "TATTA": 147, "CAGCA": 148, "CTCTT": 149, "CTTAA": 150, "CAGAA": 151, "GCTGA": 152, "GTTAA": 153, "TCTTA": 154, "TATTTT": 155, "GCCAA": 156, "CTTTG": 157, "GACC": 158, "CGCA": 159, "GTATT": 160, "GTCTT": 161, "CAATT": 162, "GTGTT": 163, "CTCAA": 164, "GGAGG": 165, "CGAA": 166, "TCTTTT": 167, "GTCAA": 168, "CGCC": 169, "TATAA": 170, "TACC": 171, "TCTAA": 172, "CCATT": 173, "CGGA": 174, "CAAAAA": 175, "CAGTG": 176, "TCCTG": 177, "CTCTG": 178, "GAAAAA": 179, "CTGTG": 180, "CAGC": 181, "TTTTAA": 182, "GCATT": 183, "GCCTT": 184, "TAATG": 185, "CTATT": 186, "GTTTG": 187, "TGATG": 188, "GGCTG": 189, "CCTCA": 190, "GAGGA": 191, "GCCTG": 192, "AAATT": 193, "CGTA": 194, "TCAAAA": 195, "TACAA": 196, "CATCA": 197, "CAGTT": 198, "TGAGA": 199, "GGGAA": 200, "CACTG": 201, "CACAA": 202, "CAGGA": 203, "CCCCA": 204, "CCCTG": 205, "TTTTTTTT": 206, "TAGAA": 207, "GAGCA": 208, "CCTCC": 209, "CACCA": 210, "TATCA": 211, "GAGC": 212, "CATTA": 213, "CACACACA": 214, "GAGTG": 215, "GGATT": 216, "TGTGTGTG": 217, "TACTT": 218, "CACTT": 219, "GTCTG": 220, "TGAGG": 221, "GAGTT": 222, "GAATG": 223, "TCATG": 224, "GACAA": 225, "GACTT": 226, "TATTAA": 227, "TAATAA": 228, "GGCCA": 229, "CATTTT": 230, "CAGCC": 231, "CCCTT": 232, "GCTAA": 233, "TATATATA": 234, "GTGTG": 235, "TACTG": 236, "TAGTT": 237, "CAATG": 238, "GCTC": 239, "CAGTA": 240, "GCTCC": 241, "CATAA": 242, "TTATG": 243, "TAAATT": 244, "GATGA": 245, "CATGA": 246, "GCGG": 247, "AAAAAAAA": 248, "CCATG": 249, "GATAA": 250, "GACTG": 251, "TATGA": 252, "GCAGG": 253, "GATCA": 254, "GTTTTA": 255, "GGATG": 256, "CCTGA": 257, "GTAAAA": 258, "GAAGG": 259, "GATTA": 260, "CCTC": 261, "GACCA": 262, "GCTTA": 263, "CCCAA": 264, "AAATG": 265, "GCATG": 266, "TAGTA": 267, "TACCA": 268, "GGCTT": 269, "CGTC": 270, "TCTCTT": 271, "GGTCA": 272, "TTATTA": 273, "TACTA": 274, "TAGCA": 275, "TATC": 276, "CTGGG": 277, "CATC": 278, "CTTTTA": 279, "CTAAAA": 280, "GTGGG": 281, "GAGTA": 282, "CCAGG": 283, "GATTTT": 284, "TAGTG": 285, "GAAATT": 286, "CACTA": 287, "TCGG": 288, "TCAGG": 289, "CAGGAA": 290, "GCAAAA": 291, "CCTTA": 292, "CATCC": 293, "CTTGG": 294, "TGTGAA": 295, "TATTTG": 296, "CCTAA": 297, "CTATG": 298, "GAGAAA": 299, "GAGAGAGA": 300, "GCTTTT": 301, "TATAAA": 302, "CAAGG": 303, "TCTCTG": 304, "TGTTAA": 305, "TGTGTT": 306, "GAGCC": 307, "GACTA": 308, "TATATT": 309, "TAAAAAA": 310, "TTTTTG": 311, "GTATG": 312, "CATTAA": 313, "TAGGA": 314, "TAGC": 315, "GTTGG": 316, "GAAGAA": 317, "TAAATG": 318, "TCTGTT": 319, "CAGAAA": 320, "CAAATT": 321, "TAATTA": 322, "TCTGTG": 323, "TATCC": 324, "TGAATT": 325, "CTCCA": 326, "GTGAAA": 327, "GGCAA": 328, "GGAGA": 329, "GAAGA": 330, "GGTGA": 331, "GGGCA": 332, "CCAAAA": 333, "TCTCTCTC": 334, "CTGCA": 335, "CTTCTT": 336, "TCTTAA": 337, "CCCTA": 338, "TGTGTG": 339, "AAATA": 340, "TGTTTG": 341, "GGGTT": 342, "GTGCTG": 343, "GGAAAA": 344, "GGGGA": 345, "TCAGA": 346, "CCTTTT": 347, "GAAATG": 348, "GCAGCA": 349, "TCTGAA": 350, "GGGTG": 351, "CACATT": 352, "TCTTTG": 353, "GGGC": 354, "TCCCA": 355, "TCCATT": 356, "CTGAAA": 357, "CTTTA": 358, "TCGA": 359, "GTTTA": 360, "CAACAA": 361, "CTTCC": 362, "GCCTCC": 363, "TTAAA": 364, "GCTCTG": 365, "GTTTCA": 366, "GGAGGA": 367, "CGTGA": 368, "CAGTC": 369, "GAATA": 370, "CAGAGA": 371, "CCCTC": 372, "CAAATG": 373, "CTGCTG": 374, "GATCC": 375, "TTTTATT": 376, "AAAATT": 377, "TTATA": 378, "TCAATT": 379, "GGTAA": 380, "GTTATT": 381, "GCCAGG": 382, "GGAGAA": 383, "CATTTG": 384, "TCACC": 385, "CTCAAA": 386, "GGTTA": 387, "TCCAAA": 388, "TCTATT": 389, "GCAGA": 390, "CTTCA": 391, "TCATCA": 392, "CGAGG": 393, "TAACA": 394, "GTTGTT": 395, "CTTATT": 396, "CGTCA": 397, "TAAGA": 398, "TAATTTT": 399, "CTGTA": 400, "TCCACA": 401, "GCTGTG": 402, "CGCTG": 403, "TCTAAA": 404, "GCGA": 405, "CAATA": 406, "CCACCA": 407, "GAACA": 408, "CGAAA": 409, "CAGATT": 410, "TCACA": 411, "TTATTTT": 412, "TCTCAA": 413, "TGACA": 414, "CTCCAA": 415, "AAAAAAA": 416, "TATATG": 417, "TCCTCC": 418, "TCACTT": 419, "TCCAGG": 420, "CAAGA": 421, "GGCTA": 422, "GTGGTG": 423, "CGTAA": 424, "CGAGA": 425, "TGATA": 426, "GGATTA": 427, "CAACA": 428, "CGATT": 429, "TGAGAA": 430, "CTCCTT": 431, "CTCATT": 432, "GTTAAA": 433, "TCATA": 434, "CCTCTG": 435, "CTCTA": 436, "GCTGAA": 437, "CTGGA": 438, "TAAGG": 439, "CTTAAA": 440, "TATTTA": 441, "CCACA": 442, "CCGG": 443, "GTCAAA": 444, "TGGAA": 445, "CGGAA": 446, "TGATGA": 447, "GTTCA": 448, "TAACAA": 449, "GCTGTT": 450, "TAAGAA": 451, "CTGCC": 452, "TTAATT": 453, "CCAGA": 454, "TCAGAA": 455, "GTCATT": 456, "CGCTT": 457, "GATTAA": 458, "CTGATT": 459, "GCCACA": 460, "GTAATT": 461, "TCCAGA": 462, "GCCAAA": 463, "GTGATT": 464, "TAAAATT": 465, "CAAGAA": 466, "CCACC": 467, "TAATCC": 468, "GTTCTT": 469, "TCCATG": 470, "GCTCTT": 471, "TGCTG": 472, "GGGTA": 473, "TTACA": 474, "GCCATT": 475, "GCACA": 476, "GCAATT": 477, "TCCCTG": 478, "TGTGA": 479, "TCGAA": 480, "GGACA": 481, "GGAATT": 482, "GTGGA": 483, "CTTCTG": 484, "TCCCC": 485, "GCCCC": 486, "CTTGA": 487, "TAATGA": 488, "TAAATA": 489, "TATATA": 490, "CTGCAA": 491, "TCATTA": 492, "GTATA": 493, "TCCCCA": 494, "CGTTA": 495, "GCAGAA": 496, "TGAGTT": 497, "CTTTTTT": 498, "CGATG": 499, "CTTTCA": 500, "AAAATG": 501, "CAGGTT": 502, "CTAATT": 503, "CGCCA": 504, "TGAAAAA": 505, "GTTCC": 506, "GTCCTT": 507, "GTCCAA": 508, "GTTTTTT": 509, "CTCTGA": 510, "GCGC": 511, "GTTGA": 512, "TGAATG": 513, "CTATA": 514, "GCAGTG": 515, "CCTTAA": 516, "TCACCA": 517, "TCACTG": 518, "GCCCTG": 519, "TAACTT": 520, "CAGATG": 521, "GTAGG": 522, "TCTATA": 523, "GAGATT": 524, "GTCTA": 525, "TTTTAAA": 526, "CACATG": 527, "TGACC": 528, "CACAAA": 529, "GTGTA": 530, "GGGAGG": 531, "GCTTTG": 532, "CAAAAAA": 533, "GAGGAA": 534, "GTTCTG": 535, "TTTTTA": 536, "GTCTCA": 537, "GTTCAA": 538, "TCGTG": 539, "GCTTAA": 540, "GCACC": 541, "CTCCTG": 542, "TAAATAAA": 543, "CTACA": 544, "CTTCCA": 545, "TCCTCA": 546, "CGCAA": 547, "GAAAAAA": 548, "GCCCA": 549, "TCGTT": 550, "GTAGA": 551, "CTCTCA": 552, "GTCCA": 553, "TGACTT": 554, "TCCCTT": 555, "GCCATG": 556, "CACACACACACACACA": 557, "GTGATG": 558, "CCTCTT": 559, "GCCAGA": 560, "TCCTA": 561, "CGTTTT": 562, "GTACA": 563, "GCATA": 564, "GAATTA": 565, "TGTGTGTGTGTGTGTG": 566, "CCCAGG": 567, "GGTTTT": 568, "TCAAAAA": 569, "TCTATG": 570, "CCATA": 571, "TGACAA": 572, "GGATA": 573, "TCAGTG": 574, "GTATTTT": 575, "GAGATG": 576, "GCGTG": 577, "CGTCC": 578, "TTAAAAA": 579, "TAATCA": 580, "CAATTA": 581, "CCACTG": 582, "CGGTT": 583, "GTTGAA": 584, "TGATTA": 585, "CCTTTG": 586, "CGGTG": 587, "CAGGTG": 588, "TCAATG": 589, "CTGATG": 590, "TCAGGA": 591, "GTTTAA": 592, "TATTAAA": 593, "CTCTTA": 594, "GCAGGA": 595, "CTCTCC": 596, "GAACC": 597, "CTTTAA": 598, "GGGCC": 599, "GTATTA": 600, "GCGCC": 601, "CCAATT": 602, "GCTAAA": 603, "TGACTG": 604, "GATTTG": 605, "GATAAA": 606, "TCAGCA": 607, "GTTCCA": 608, "GAAATA": 609, "GACAAA": 610, "GAGTC": 611, "GCTATT": 612, "TCACAA": 613, "GAGGTT": 614, "TAACC": 615, "GAAGGA": 616, "GCTCAA": 617, "GAAAATT": 618, "CCAGCA": 619, "GTTTTAA": 620, "GTGCC": 621, "TGAGGA": 622, "CATAAA": 623, "GGTCC": 624, "TCATTTT": 625, "TATTTATT": 626, "TAATAAA": 627, "GCCTA": 628, "CTTTTAA": 629, "TAAGTG": 630, "TAAGTA": 631, "CTGGAA": 632, "CACACA": 633, "GACAGA": 634, "CAACC": 635, "GGGAAA": 636, "CCAGAA": 637, "TCAGTT": 638, "TAACTA": 639, "CTAAAAA": 640, "TGGGTT": 641, "TGAGTG": 642, "TAAAATG": 643, "TATATATATATATATA": 644, "GCACTG": 645, "GACTC": 646, "TACAAA": 647, "TAAAAAAA": 648, "TCTACA": 649, "GTTGTG": 650, "TCGCC": 651, "CCCAAA": 652, "GTCATG": 653, "CTGCTT": 654, "GGAATG": 655, "CTATTA": 656, "GATATT": 657, "TAGAAA": 658, "GGCAGG": 659, "GATGAA": 660, "GTAGAA": 661, "TCCTGA": 662, "TAACTG": 663, "GCTGGG": 664, "GCAATG": 665, "GCCCCA": 666, "GTTTGA": 667, "CATTTA": 668, "GTGCA": 669, "CTTGAA": 670, "GTGGAA": 671, "CTTCAA": 672, "TAAATTA": 673, "GTGGCA": 674, "TCCTTA": 675, "GGAAAAA": 676, "TTTTTTA": 677, "CCTGTG": 678, "GTAATG": 679, "GTGTTA": 680, "CTAGG": 681, "CAGGCTG": 682, "GACACA": 683, "GAAAAAAA": 684, "TCGC": 685, "GTAAAAA": 686, "TGTTTA": 687, "TCTCTA": 688, "GTCCTG": 689, "CCAGGA": 690, "GAACAA": 691, "TAAGTT": 692, "TGAGCA": 693, "GCTCCA": 694, "TAAGCA": 695, "CTCATG": 696, "GTCTTA": 697, "CCCACA": 698, "CATATT": 699, "GCCTCA": 700, "CACTC": 701, "CTTCTA": 702, "TGATTTT": 703, "TCGCA": 704, "CCTGTT": 705, "GAAGCA": 706, "GCAAAAA": 707, "GCGGA": 708, "CCACAA": 709, "GCGCA": 710, "CATATA": 711, "GACATT": 712, "GTTCTA": 713, "CAAAATT": 714, "GAAAGAAA": 715, "CCCGG": 716, "TACACA": 717, "CCAAAAA": 718, "GAGGTG": 719, "GGCTCA": 720, "CAGTGA": 721, "TCCCAA": 722, "TATCTT": 723, "TGAGTA": 724, "TCGTA": 725, "TTTTCTT": 726, "GTGGGA": 727, "GAGCTG": 728, "CCCTCC": 729, "TAGGTT": 730, "TTAGG": 731, "TAATATT": 732, "CCAGCC": 733, "CATCTT": 734, "GTCTGA": 735, "GTTTCC": 736, "CCTGAA": 737, "GGAGCA": 738, "GAAAATG": 739, "TCAGTA": 740, "TAACCA": 741, "GATGTT": 742, "CTGTTA": 743, "CATGTT": 744, "GGCGG": 745, "CATGTG": 746, "GGGAGA": 747, "CTTTGA": 748, "TCTTTCTT": 749, "AAAAAAAAA": 750, "GGGGTG": 751, "CTTTCC": 752, "CTTGTT": 753, "GCATTA": 754, "CCCAGA": 755, "CAAATA": 756, "TCGGA": 757, "CAGCTT": 758, "TCACTA": 759, "TAATTAA": 760, "TAAGGA": 761, "GAACTG": 762, "GCACAA": 763, "GCGTT": 764, "GGCTC": 765, "TCTTTTA": 766, "CCTCCA": 767, "GGCAAA": 768, "CAGCTG": 769, "CTACAA": 770, "TACATT": 771, "GCTATG": 772, "CTTGTG": 773, "GAGTCA": 774, "GTTATG": 775, "CTGCCA": 776, "GTCTCC": 777, "TGACCA": 778, "CACCTG": 779, "TATATTA": 780, "TGATCA": 781, "CAGCAA": 782, "GATGTG": 783, "GTCTTTT": 784, "CTAGAA": 785, "GCTACA": 786, "CTGGGA": 787, "GGGGTT": 788, "CAAGTA": 789, "CAAGGA": 790, "CCCTCA": 791, "TAGCC": 792, "GTTGGA": 793, "GCTATA": 794, "TCTGAAA": 795, "TATGTT": 796, "CCCCTT": 797, "GTTGTA": 798, "CCCTGA": 799, "TGACTA": 800, "CAAGCA": 801, "CAATAA": 802, "GAACTT": 803, "CATGAA": 804, "CTTATG": 805, "CTAATG": 806, "TCTAAAA": 807, "CCAATG": 808, "GAAGTG": 809, "CCTCAA": 810, "CCCATT": 811, "CAGTCA": 812, "GAGAGAGAGAGAGAGA": 813, "TATGTG": 814, "GCAGTGA": 815, "TCTCCTT": 816, "TCCCAAA": 817, "CCATTA": 818, "CCAGTG": 819, "GCATCA": 820, "TCAAATT": 821, "GATCTT": 822, "GACAGG": 823, "GGAGTG": 824, "GTAGTA": 825, "CAACTT": 826, "GAAGTT": 827, "CCCCTG": 828, "TCTCAAA": 829, "GGGTC": 830, "GAGCTT": 831, "TATGAAA": 832, "TATGAA": 833, "GACATG": 834, "CAAGTG": 835, "GATATA": 836, "CATCTG": 837, "CTGTGA": 838, "TAATTTA": 839, "GGCAGA": 840, "GCGAA": 841, "CCTAAA": 842, "CCATCA": 843, "CACTGA": 844, "GGACTA": 845, "GACGG": 846, "CTCTTTT": 847, "CTGTCA": 848, "TCTCTCTCTCTCTCTC": 849, "TTAATG": 850, "GCAGCC": 851, "CAAAAAAA": 852, "GCACCA": 853, "CTATTTT": 854, "GAGCAA": 855, "CTTGGA": 856, "CTGGTG": 857, "GAATAA": 858, "TCCTTTT": 859, "GAAGTA": 860, "CAGTAA": 861, "CAACCA": 862, "CTGTAA": 863, "TGATAA": 864, "GCAGTT": 865, "CACGG": 866, "TAAATAA": 867, "CTGTTTT": 868, "CTACTA": 869, "GCTCTA": 870, "CGAAAA": 871, "CAAGTT": 872, "CTTGTA": 873, "GAATGA": 874, "GAGTGA": 875, "GCCTGA": 876, "GGTTTG": 877, "CCCATG": 878, "GGGGAA": 879, "GAAGAAA": 880, "TGTTA": 881, "CAATTTT": 882, "TATATTTT": 883, "CTCAAAA": 884, "GGTGGG": 885, "CCGTG": 886, "TATTTCA": 887, "CCCCAA": 888, "TATTTAA": 889, "GGCTGA": 890, "GGTGTG": 891, "CATCAA": 892, "CACTCA": 893, "TCTCATT": 894, "GAATTTT": 895, "GAATCA": 896, "CAGGAAA": 897, "CATACA": 898, "TATTTTA": 899, "TTATAA": 900, "GAGGAAA": 901, "CATATG": 902, "CTTTCTT": 903, "CAACTG": 904, "GGGCTG": 905, "CCCCCA": 906, "TTTGAAA": 907, "CATTAAA": 908, "CTTAAAA": 909, "GACTGA": 910, "CAATGA": 911, "GGCACA": 912, "CCAGTA": 913, "GGATGA": 914, "GTTTTTG": 915, "GCATTTT": 916, "GTGCCA": 917, "GCAGTA": 918, "GCCCTT": 919, "TCGTC": 920, "GAACTA": 921, "GTGGTT": 922, "GTGTGA": 923, "GTGCTT": 924, "CGCTA": 925, "GTGTCA": 926, "TCTTTA": 927, "GCCTTA": 928, "CCTATT": 929, "CAAAATG": 930, "GAACCA": 931, "CTCCAGG": 932, "GACTCA": 933, "CATGAAA": 934, "GCTAGG": 935, "TGTTAAA": 936, "GCGTA": 937, "GCACTT": 938, "TCTTAAA": 939, "TAAGAAA": 940, "GGCCTG": 941, "TCCCTA": 942, "GTGGTA": 943, "CTGCTA": 944, "GGAGTT": 945, "GGTAAA": 946, "CAAACAAA": 947, "GATATG": 948, "TCATGA": 949, "GACCTT": 950, "TAATATA": 951, "GCTAGA": 952, "GGACTG": 953, "GGCATT": 954, "CAGTTA": 955, "CCCTAA": 956, "CACCTT": 957, "GGTGAA": 958, "CAGCTA": 959, "GTGTTTT": 960, "CAACTA": 961, "GATCAA": 962, "GAGAAAA": 963, "TGTGAAA": 964, "AAAATA": 965, "GATGAAA": 966, "CTCTAA": 967, "TTACTT": 968, "GATCTG": 969, "CCACTT": 970, "GAGTTA": 971, "CAATCA": 972, "GGATTACAGG": 973, "TTTATTTT": 974, "TACATA": 975, "TTTTATG": 976, "GAGTAA": 977, "GCTGAAA": 978, "GTACTG": 979, "GCTCTC": 980, "TATGTA": 981, "TGTGTA": 982, "TCATAA": 983, "GGACTT": 984, "TCTCCAA": 985, "GCATGA": 986, "GACGA": 987, "CGCCTG": 988, "GACCTG": 989, "GGTCTT": 990, "CACCAA": 991, "GATC": 992, "GACCAA": 993, "AAAATTA": 994, "GTAAATT": 995, "CCAGTT": 996, "CAGAAAA": 997, "TAACAAA": 998, "GGTGTT": 999, "GAAATTA": 1000, "TGCCTCA": 1001, "CCGCC": 1002, "CCATTTT": 1003, "CTTGCC": 1004, "TCTGTA": 1005, "CTGGCA": 1006, "GGGATG": 1007, "CCATGA": 1008, "CTACTT": 1009, "TAGGTG": 1010, "TAAAAATT": 1011, "GAAAGAA": 1012, "TAAAATA": 1013, "CTTTTTG": 1014, "GTCAAAA": 1015, "GGACAA": 1016, "TCTGATT": 1017, "CTCTCTT": 1018, "TAATTTG": 1019, "CTCTTTG": 1020, "GGCCTT": 1021, "GGATTTT": 1022, "CTACTG": 1023, "GTTGCA": 1024, "GGCTCC": 1025, "CTCTGTG": 1026, "CTCCAGCC": 1027, "TTACAA": 1028, "GGACCA": 1029, "GGAAGGAA": 1030, "TAAAGAA": 1031, "TTAGAA": 1032, "GTGAAAA": 1033, "CTTGCA": 1034, "TGGGTG": 1035, "GGAGCC": 1036, "CCTCTA": 1037, "CT": 1038, "GGGCTT": 1039, "GGCATG": 1040, "CTGGTT": 1041, "TACAGA": 1042, "GATTAAA": 1043, "CTCTGTT": 1044, "TTATCA": 1045, "CTGAAAA": 1046, "GTAGTT": 1047, "GGGTCA": 1048, "GT": 1049, "CAGCCA": 1050, "GCGTC": 1051, "CACTTA": 1052, "GTGCTA": 1053, "TCTTATT": 1054, "GTACTT": 1055, "GGTATT": 1056, "TAGAGA": 1057, "TACATG": 1058, "CCACTA": 1059, "TGAGAAA": 1060, "CAATAAA": 1061, "TCCAAAA": 1062, "CGTGAA": 1063, "GGTCTG": 1064, "CTGAATT": 1065, "TCAGCC": 1066, "CCTCTC": 1067, "GTTAAAA": 1068, "GGGATT": 1069, "TCCTAA": 1070, "CACTAA": 1071, "GGAGAAA": 1072, "CCTTCCTT": 1073, "GTTTCTT": 1074, "TATCAA": 1075, "GATACA": 1076, "TAATCCCAGCA": 1077, "CCGCA": 1078, "TGAAATT": 1079, "CGTAAA": 1080, "CTCTCTG": 1081, "TCTTTTTT": 1082, "GTACAA": 1083, "CCAAATT": 1084, "TGTATTTT": 1085, "TCGCTT": 1086, "GGGTGA": 1087, "GATAGA": 1088, "CTTTATT": 1089, "TAAACAA": 1090, "GTTTATT": 1091, "TGAATA": 1092, "CTACCA": 1093, "GTGTCC": 1094, "CCCGA": 1095, "TTTATTA": 1096, "CTCCAAA": 1097, "TTTTTTTTTTTT": 1098, "TCATCC": 1099, "GAAGCC": 1100, "CTAAATT": 1101, "CAAATTA": 1102, "CCCCAAA": 1103, "TCTTCTT": 1104, "TAGGAAA": 1105, "CACGA": 1106, "CATTTTA": 1107, "GTGCAA": 1108, "TCTCCTG": 1109, "TATTTTAA": 1110, "GTTTGTT": 1111, "GAGCCA": 1112, "GGCCAA": 1113, "CATTTCA": 1114, "CATCCA": 1115, "CCTATA": 1116, "GACTTA": 1117, "TCAAATG": 1118, "GTATCA": 1119, "TAAATTTT": 1120, "CTGAGGCA": 1121, "GCCCAA": 1122, "GGTTAA": 1123, "TATCTG": 1124, "TGACAGA": 1125, "GGAGAGA": 1126, "GCTGCTG": 1127, "CCCTTA": 1128, "TCCTCTG": 1129, "GTAGCA": 1130, "CCTGAAA": 1131, "CCGAA": 1132, "TTTTTAA": 1133, "CTATAA": 1134, "CCTGTA": 1135, "TTACTG": 1136, "GTATAA": 1137, "GGCGA": 1138, "GACTAA": 1139, "TCAGAAA": 1140, "GTGTGTG": 1141, "CAAAGAA": 1142, "CCTATG": 1143, "GCAGAGA": 1144, "CCGTT": 1145, "TTTTATTTT": 1146, "GGAAGAA": 1147, "TTACTA": 1148, "GCCTGGG": 1149, "TCCCTC": 1150, "TCCTCTT": 1151, "GGATCA": 1152, "GGTCAA": 1153, "TCGAGA": 1154, "TATTCTT": 1155, "TACTC": 1156, "GTTAATT": 1157, "GCGAGA": 1158, "CTTAATT": 1159, "TCCTTTG": 1160, "GTCTAA": 1161, "CACCCA": 1162, "GGGTTA": 1163, "GGGCAA": 1164, "GGAAATG": 1165, "GCAAATT": 1166, "TAGATG": 1167, "GCAGAAA": 1168, "AAAAAAAAAAAAAAAA": 1169, "CCTACA": 1170, "GGAGTA": 1171, "TCTAATT": 1172, "CAACAAA": 1173, "TAGATT": 1174, "GGTTTA": 1175, "CCTAGA": 1176, "CTTTAAA": 1177, "TACTTA": 1178, "TAATGAA": 1179, "CTATCA": 1180, "TAGTAA": 1181, "CAGAGAA": 1182, "CAAGAAA": 1183, "GGGGAAA": 1184, "CGTTAA": 1185, "CGTGTT": 1186, "TCTGTCTG": 1187, "TTTTAATT": 1188, "CTGGCC": 1189, "TAAATGA": 1190, "CGTCAA": 1191, "TTAGTA": 1192, "GTCTCTG": 1193, "TTTTAAAA": 1194, "CAGTTTT": 1195, "CTTCCTT": 1196, "TATATAA": 1197, "GCTTTTA": 1198, "TTTTTCA": 1199, "GGTC": 1200, "TTATTAA": 1201, "TTTTGTT": 1202, "CATAGA": 1203, "TAGGAA": 1204, "GAGAGAA": 1205, "GTAGCTG": 1206, "TTATGA": 1207, "GTAGTG": 1208, "GGAGAGG": 1209, "CTCTGAA": 1210, "TAGTC": 1211, "GACTCC": 1212, "TCCCTCC": 1213, "TAATGTT": 1214, "CATCTA": 1215, "GCCACCA": 1216, "GTACTA": 1217, "TGGGAAA": 1218, "CGCCTT": 1219, "GCCCGG": 1220, "GGAGGAA": 1221, "GTACCA": 1222, "CGCAAA": 1223, "CATAAAA": 1224, "TAACATT": 1225, "GCTAAAA": 1226, "TCTTCTG": 1227, "GCCAAAA": 1228, "GTATGA": 1229, "GTCTTTG": 1230, "TACTGA": 1231, "TCCCAGG": 1232, "TTATTTA": 1233, "TTAGTT": 1234, "GGACC": 1235, "TATAAAA": 1236, "CAAACAA": 1237, "CTTCTC": 1238, "TCTATCTA": 1239, "GAAATAA": 1240, "GTGTAA": 1241, "CTTTGTT": 1242, "GATAAAA": 1243, "GCCCAGG": 1244, "GCGATT": 1245, "AAAAAATT": 1246, "TACAGG": 1247, "GGCTAA": 1248, "TAGCTT": 1249, "GTCTCTA": 1250, "CTCCTGA": 1251, "GAATAAA": 1252, "TTACCA": 1253, "GGGACA": 1254, "GCCACTG": 1255, "GTTTAAA": 1256, "GTCTGTG": 1257, "TGACAAA": 1258, "TACATTTT": 1259, "GCCACC": 1260, "TGTTTT": 1261, "TAGCAA": 1262, "TTATAAA": 1263, "GACCCA": 1264, "GCAGC": 1265, "CAGACAGA": 1266, "CACAAAA": 1267, "GCCCTA": 1268, "TATTAAAA": 1269, "CGTATT": 1270, "CCATCC": 1271, "TCGATT": 1272, "GAAGGAA": 1273, "GATCCA": 1274, "TATTTGA": 1275, "GTGAATT": 1276, "TACCTT": 1277, "CGTCTT": 1278, "CCTAGG": 1279, "TCGAAA": 1280, "CTTTCTG": 1281, "TGAAGAA": 1282, "TCTCTCA": 1283, "GTCTCTT": 1284, "GGAGGGG": 1285, "GTCTGTT": 1286, "CTATGA": 1287, "GGAAATT": 1288, "GCACACA": 1289, "GCCTTTT": 1290, "CAGTCC": 1291, "CTGGTA": 1292, "GCATCC": 1293, "TAGTTA": 1294, "GGCTTA": 1295, "GAGTCC": 1296, "TGAAAA": 1297, "TAGATAGA": 1298, "TGTTTGTT": 1299, "TACTCA": 1300, "CATTTAA": 1301, "GATTTTA": 1302, "CACTCC": 1303, "GAAACAA": 1304, "GCGCTG": 1305, "TCTTTCA": 1306, "CTGTCC": 1307, "GAACTCA": 1308, "CGGAAA": 1309, "TATTGTT": 1310, "GCACTA": 1311, "TATTCAA": 1312, "GCGGGG": 1313, "GTGGCC": 1314, "TAATTAAA": 1315, "TACTAA": 1316, "GCGGTG": 1317, "TACCAA": 1318, "GGTATA": 1319, "CTAGTT": 1320, "GCAGAGG": 1321, "CTTTTTTTT": 1322, "TTTTTTTTTTTTTTTT": 1323, "TACAGTA": 1324, "CCATGTT": 1325, "TAGTGA": 1326, "CGTGTG": 1327, "GCTCTGA": 1328, "CTTCCTG": 1329, "TCGCTG": 1330, "TAAATCA": 1331, "TCCAATT": 1332, "GTTTCTG": 1333, "GAAGAGA": 1334, "GGGTAA": 1335, "CCATAA": 1336, "TTATATT": 1337, "CGAATT": 1338, "CCGGA": 1339, "TGAGCC": 1340, "CCGTA": 1341, "CAGAGGA": 1342, "GTGTTTG": 1343, "GACAAAA": 1344, "TTTTTTAAA": 1345, "GTTGCC": 1346, "GAGTTTT": 1347, "TCAAAAAA": 1348, "TGTTTCA": 1349, "TATCTA": 1350, "TCTCTCC": 1351, "CTCCACA": 1352, "TAAATATT": 1353, "TTTTCTG": 1354, "CTCTCAA": 1355, "CCTTAAA": 1356, "TCTTTTAA": 1357, "GAACAAA": 1358, "TTAGCA": 1359, "GCTCATG": 1360, "TAAAGTA": 1361, "GGATAA": 1362, "TTATTAAA": 1363, "CTCCATT": 1364, "TCTCTGA": 1365, "TTATTTG": 1366, "CCTGTAA": 1367, "TTATATA": 1368, "GACTTTT": 1369, "TGTTGTT": 1370, "GCAAATG": 1371, "CTTCAAA": 1372, "GAATATT": 1373, "GAATCC": 1374, "CTCTTAA": 1375, "GCATAA": 1376, "GAATGAA": 1377, "CTTAAAAA": 1378, "TAAAAATG": 1379, "TTTTAAAAA": 1380, "CTCTGGG": 1381, "TGATCC": 1382, "GCTCTCA": 1383, "CTCCAGA": 1384, "GAGTGCAGTG": 1385, "CAATATT": 1386, "TAGAAAA": 1387, "GTAAATG": 1388, "TAGCTG": 1389, "GCTCAAA": 1390, "GCAGGAA": 1391, "TACCTG": 1392, "GGGAAAA": 1393, "TTTTCTA": 1394, "GGGGGGGG": 1395, "CCGA": 1396, "CTTTGAA": 1397, "GGAGGTG": 1398, "TAGTCA": 1399, "GGCCCA": 1400, "TGATGTT": 1401, "CAAATAA": 1402, "TCTTCCA": 1403, "GCGCTT": 1404, "GTATTTG": 1405, "GTCTC": 1406, "GAAATCA": 1407, "TGATAAA": 1408, "CATTCTT": 1409, "TATCCA": 1410, "GCCTCTG": 1411, "TGAGATG": 1412, "CGCCAA": 1413, "GTTTTATT": 1414, "TATATATT": 1415, "GTAGGA": 1416, "GACAGAA": 1417, "CTCCAGCCTGGG": 1418, "GCGTGA": 1419, "GGTATG": 1420, "GAGGGAGG": 1421, "TCATTTG": 1422, "CTACC": 1423, "TACAGAA": 1424, "GGTAGA": 1425, "GATCTA": 1426, "GTCCATG": 1427, "TGAGGAA": 1428, "TAATAAAA": 1429, "TAAACTT": 1430, "TCACATT": 1431, "GGAGGCC": 1432, "TCACAAA": 1433, "CACTTTT": 1434, "CGGCC": 1435, "CAACAGA": 1436, "GTAGAGA": 1437, "GTTATTTT": 1438, "CGTTTG": 1439, "TCGTCA": 1440, "TCTGCTG": 1441, "CAACACA": 1442, "GGTAGG": 1443, "GCAGCTG": 1444, "TAGTAGAGA": 1445, "CAAGCC": 1446, "GCATTTG": 1447, "TAATATG": 1448, "GCTTAAA": 1449, "GCTTCTG": 1450, "CTCTCCA": 1451, "TCATCTT": 1452, "CGTCTG": 1453, "TCATTTA": 1454, "CATAGG": 1455, "GCTCCTT": 1456, "TGTTCTT": 1457, "TACATTA": 1458, "CACAGAA": 1459, "TAAATATA": 1460, "TAGAGG": 1461, "GATAGG": 1462, "TCCTGAA": 1463, "GGAGCTG": 1464, "TGATATT": 1465, "TCATTAA": 1466, "CTTTTAAA": 1467, "TCGTTA": 1468, "TAAACTA": 1469, "GTTTGAA": 1470, "TAAAATTA": 1471, "CACCCC": 1472, "TCAGAGA": 1473, "CTCCTGCCTCA": 1474, "TGACATT": 1475, "GTATTTA": 1476, "CTTCATT": 1477, "GAAACTG": 1478, "TAACACA": 1479, "GTTCAAA": 1480, "GGAGATG": 1481, "TCGGCC": 1482, "CAGCATT": 1483, "TCGATG": 1484, "TATTCTA": 1485, "CTGTGAA": 1486, "TATTGAA": 1487, "TTTTCCA": 1488, "TATTTCTT": 1489, "GGTGAAA": 1490, "CTGAGAA": 1491, "GCACAGA": 1492, "GCGAGG": 1493, "CTGTGTG": 1494, "TGAAATG": 1495, "TGATGAA": 1496, "GTCCAAA": 1497, "CTCAATT": 1498, "TCCAGAA": 1499, "GTATATA": 1500, "TAAAGTT": 1501, "TCTCAAAA": 1502, "TCCATCA": 1503, "GTCTGAA": 1504, "TGAGAGA": 1505, "TGATTTG": 1506, "TTAGCC": 1507, "CTCCATG": 1508, "TCCCTGA": 1509, "GAGCTA": 1510, "CCCCCCCC": 1511, "GTGGAAA": 1512, "CTGGGAA": 1513, "CAATGAA": 1514, "CCACACA": 1515, "CTTTCAA": 1516, "CGGAGG": 1517, "TCGTGA": 1518, "CCAGAAA": 1519, "GTTTTAAA": 1520, "TGTTGAA": 1521, "TCCTGTG": 1522, "CTAAATG": 1523, "TCCTTTA": 1524, "GTCTGGG": 1525, "TCTCTTTT": 1526, "TACGG": 1527, "TATTGTA": 1528, "TTAGTG": 1529, "TTACC": 1530, "TAATCCCAGCACTTTG": 1531, "TCTGGAA": 1532, "CTTCTCA": 1533, "CGCATT": 1534, "TATTTAAA": 1535, "TCACACA": 1536, "TAATCAA": 1537, "GCGAAA": 1538, "GGGCCA": 1539, "GTTCATT": 1540, "GAGAAAAA": 1541, "TTTTGTA": 1542, "TACTTTT": 1543, "TCGAGG": 1544, "GTGAAAAA": 1545, "CAATATA": 1546, "TCCCATG": 1547, "CAATTAA": 1548, "CTGGAAA": 1549, "CCCAGCA": 1550, "TCCCATT": 1551, "TCCTGTT": 1552, "CTCTTTA": 1553, "TCCCCTT": 1554, "GTTTCAA": 1555, "GTCCAGG": 1556, "GGAAGGA": 1557, "TAGTTTT": 1558, "TGACCTT": 1559, "GTGCTGGGATTACAGG": 1560, "TATTTATA": 1561, "TCTGCAA": 1562, "CTGAAAAA": 1563, "TATGTTA": 1564, "CTTCACA": 1565, "GCACAGG": 1566, "CCTGCTG": 1567, "TTTTTTAA": 1568, "GTTATTA": 1569, "CCCTTTT": 1570, "TGATTTA": 1571, "TACAAAA": 1572, "TAAGTAA": 1573, "TTTTTAAA": 1574, "CATCTC": 1575, "GTGGTGA": 1576, "GTGGAGA": 1577, "CTCTGCA": 1578, "GTTAAAAA": 1579, "TACATACA": 1580, "CTTTGTG": 1581, "GGACACA": 1582, "TCTGATG": 1583, "TATTATT": 1584, "TCTTCTA": 1585, "CTGTGTT": 1586, "TCAGCTT": 1587, "CTTTATA": 1588, "GGCGC": 1589, "TCCCTCA": 1590, "GTACC": 1591, "TGGAGAA": 1592, "CAAAAATT": 1593, "TCTTTAA": 1594, "CTCTCTC": 1595, "TGAGTGA": 1596, "GCAGCTT": 1597, "CGGATT": 1598, "TACGA": 1599, "TCTTGTT": 1600, "TCGTAA": 1601, "GCCTGTG": 1602, "TATTCTG": 1603, "GGGATA": 1604, "GGGTCC": 1605, "TGAGATT": 1606, "CTTTTATT": 1607, "TCCCACA": 1608, "CATGGTG": 1609, "TTAGGA": 1610, "GAACACA": 1611, "TCATAAA": 1612, "CAACATT": 1613, "GGTCCA": 1614, "GAATTTG": 1615, "TATTAATT": 1616, "TCCTGGG": 1617, "GCAGCAA": 1618, "CTCTTCA": 1619, "GAAGAGG": 1620, "TCTGTCA": 1621, "CTGAATG": 1622, "CCACAAA": 1623, "GTGGAGG": 1624, "TGATTAA": 1625, "CTCCCTCC": 1626, "CACACACACACACACACACACACACACACACA": 1627, "GCGATG": 1628, "CATTCTG": 1629, "GTAGAAA": 1630, "TCATCAA": 1631, "TTTTCAA": 1632, "TATGTATG": 1633, "CCAAATG": 1634, "TAATTTTA": 1635, "TAAGGAA": 1636, "CTTGAAA": 1637, "AAAAAAAAAAAA": 1638, "GCTCCTG": 1639, "GCAGATG": 1640, "GAAAAATT": 1641, "GACGC": 1642, "GTGGGGG": 1643, "GTCAATT": 1644, "CTTGCTT": 1645, "TGACACA": 1646, "GTGTGTT": 1647, "CCAGAGA": 1648, "CCCAGCC": 1649, "TAAAGAAA": 1650, "GTCCATT": 1651, "TAAATTAA": 1652, "CCCAAAA": 1653, "GAATTAA": 1654, "TGAATTA": 1655, "TTTTTTTG": 1656, "CCAGCTT": 1657, "CAATTTG": 1658, "CTGTTTG": 1659, "GTCTCAA": 1660, "GTTTGTG": 1661, "GGCATA": 1662, "GGTACA": 1663, "TGATGTG": 1664, "GATTTCA": 1665, "TCTGCTT": 1666, "GTAATTA": 1667, "TAAAAAAAA": 1668, "GCCGCC": 1669, "TGTGTGTGTGTGTGTGTGTGTGTGTGTGTGTG": 1670, "GCGTCA": 1671, "GCTCATT": 1672, "GAACCTG": 1673, "TAAACAAA": 1674, "GTGCTGA": 1675, "TCAGGAA": 1676, "TCCTCAA": 1677, "TCTATTTT": 1678, "TCTGTTTT": 1679, "CAGAGCA": 1680, "CCAGGAA": 1681, "GTCTTTA": 1682, "TCTTCAA": 1683, "TCAAAATT": 1684, "GCTTATT": 1685, "GTTCCTT": 1686, "CACCTA": 1687, "TCACTGA": 1688, "GAAGCAA": 1689, "TAAAGA": 1690, "TCCTTCA": 1691, "TCTCATG": 1692, "TCAGTGA": 1693, "TACACAA": 1694, "CACGTG": 1695, "CCTAAAA": 1696, "GCCTTTG": 1697, "GGCTTTT": 1698, "GTTGAAA": 1699, "GTTCTC": 1700, "CTAGA": 1701, "CTACAAA": 1702, "GCACAAA": 1703, "TTACATT": 1704, "GGCCCC": 1705, "TAATGTG": 1706, "CTGCCTT": 1707, "TCCCAGA": 1708, "GTGAATG": 1709, "GGACAGG": 1710, "GGATGTG": 1711, "GTTTATA": 1712, "TGACCAA": 1713, "GTGGCTG": 1714, "GTTCTCA": 1715, "CTTATTTT": 1716, "CTGGAGA": 1717, "TTACAAA": 1718, "GTCTTCA": 1719, "CAAGAGA": 1720, "CCATTTG": 1721, "TCACAGA": 1722, "CTAGTA": 1723, "CATTATT": 1724, "TTAGA": 1725, "GCTCTCC": 1726, "GCGCCA": 1727, "TATGTTTT": 1728, "TCCTCCA": 1729, "CAGAAAAA": 1730, "GTGGGAA": 1731, "TAATCTT": 1732, "TGAGTCA": 1733, "CTGCTC": 1734, "GTCTCCA": 1735, "TCATGTT": 1736, "GTTTCCA": 1737, "TAAGCAA": 1738, "CTAAAAATA": 1739, "TGACTGA": 1740, "TCGGTT": 1741, "TTAGAAA": 1742, "TAAGCC": 1743, "TAAAGCA": 1744, "CCTCTCC": 1745, "CCTCCTT": 1746, "TCAGATT": 1747, "TATGAAAA": 1748, "GCTGATG": 1749, "CATATTTT": 1750, "GCTCCAA": 1751, "CGGCGG": 1752, "CCACTGA": 1753, "CAGCAAA": 1754, "CTGTCTT": 1755, "CTAGCA": 1756, "TCGGGG": 1757, "CACAGCA": 1758, "GCTGATT": 1759, "CTAGGA": 1760, "TAACTC": 1761, "TCATATT": 1762, "CCTTCTT": 1763, "CTGCAAA": 1764, "CCCGC": 1765, "GGTCTA": 1766, "CCCAGGA": 1767, "GTGTCTG": 1768, "TAATAATAATAA": 1769, "TCACATG": 1770, "CAATTTA": 1771, "TATATATATATATATATATATATATATATATA": 1772, "CCACAGA": 1773, "TCAATTTT": 1774, "GTATTAA": 1775, "GAACATT": 1776, "TCTCTTA": 1777, "CTATTTG": 1778, "TCTTTCC": 1779, "GGTTAAA": 1780, "GCTAATT": 1781, "CTGCTGA": 1782, "TACCTA": 1783, "CAGGGTT": 1784, "TCGCCA": 1785, "CAAAAATTA": 1786, "CTTCTGA": 1787, "GCATGTG": 1788, "CTATTAA": 1789, "GCACATG": 1790, "CAACATG": 1791, "TCATGAA": 1792, "GAATGTT": 1793, "GGGTTTT": 1794, "CTGCCTG": 1795, "GTCCACA": 1796, "TAAACA": 1797, "CTCTGGA": 1798, "GACCCC": 1799, "GGCAAAA": 1800, "TCTGTTA": 1801, "CTAGTG": 1802, "CTATATA": 1803, "TCAGTCA": 1804, "TAACTAA": 1805, "GAAGATG": 1806, "GTCTTAA": 1807, "CAAGGAA": 1808, "GTAAAAAA": 1809, "TCCCCTG": 1810, "TCGCAA": 1811, "TCTGCCTG": 1812, "CCTTTTA": 1813, "GTCCCAGCTA": 1814, "TATATATG": 1815, "TATTGTG": 1816, "TGTGTTTT": 1817, "GCGCAA": 1818, "CACAGTG": 1819, "TAAGATT": 1820, "CTCTGTA": 1821, "GGAGGCTGA": 1822, "GGACAAA": 1823, "TATTAAAAA": 1824, "TCGTCC": 1825, "TCGGAA": 1826, "CTATAAA": 1827, "CTTCAGA": 1828, "CTAGAAA": 1829, "CATTCAA": 1830, "CACGCA": 1831, "CAGGATT": 1832, "CCATCTT": 1833, "GTAGCC": 1834, "GAATTTA": 1835, "CACGC": 1836, "CAATCC": 1837, "TGAGCAA": 1838, "GAAGCTG": 1839, "TCAATTA": 1840, "GAAGTCA": 1841, "CTGCACA": 1842, "CCACGG": 1843, "GGATCTT": 1844, "CTCCTGCCTCAGCCTCC": 1845, "TAAATGAA": 1846, "CCGTC": 1847, "TCGGTG": 1848, "TTTTATTA": 1849, "GCAGGGG": 1850, "GCAGGTG": 1851, "TCTATTA": 1852, "TAACTTA": 1853, "CTAATTTT": 1854, "CCCGCC": 1855, "TAATACA": 1856, "GGATTAAA": 1857, "TCTCTCTG": 1858, "GCTTCTT": 1859, "CATTTATT": 1860, "CCAGAGG": 1861, "GGACAGA": 1862, "GCCAATT": 1863, "TCCCCAA": 1864, "GTTGATT": 1865, "GAAGAAAA": 1866, "GCATTTA": 1867, "CTCTAAA": 1868, "CACACACACACA": 1869, "CCTCAAA": 1870, "TATAATT": 1871, "CAATGTT": 1872, "GCCCAGA": 1873, "GTATATT": 1874, "CTAAAAAA": 1875, "CCACAGG": 1876, "TAAGAGA": 1877, "TCCTTAA": 1878, "TATTTTTT": 1879, "GAATATA": 1880, "GGATTTG": 1881, "GTGTGAA": 1882, "CTGGCTT": 1883, "GCGGCA": 1884, "TCCGCC": 1885, "GCATCTT": 1886, "TCTAATA": 1887, "CTGCATT": 1888, "CTCTGCC": 1889, "TCACTCA": 1890, "TCAGCAA": 1891, "TATTATG": 1892, "CCAGCTG": 1893, "GATCTC": 1894, "GCCTCTT": 1895, "CTTCCAA": 1896, "TCCTAAA": 1897, "TCATCTG": 1898, "CTATTTA": 1899, "CTGCAGG": 1900, "CAAGCAA": 1901, "GCGGAA": 1902, "GAAATAAA": 1903, "TAAAATAA": 1904, "TCACCTT": 1905, "CCATGTG": 1906, "GACCTA": 1907, "CAGATGA": 1908, "GTGGCTT": 1909, "TTATTATTATTA": 1910, "TCCCGG": 1911, "TATTTGTT": 1912, "CTGTAAA": 1913, "TCCATCCA": 1914, "CTGTATA": 1915, "GTTTCTA": 1916, "GTTGCTT": 1917, "CCATGAA": 1918, "GCTCTTA": 1919, "CTTCATG": 1920, "GTTCCTG": 1921, "GCTGGGA": 1922, "TCAGAGG": 1923, "CATTAAAA": 1924, "TCAGTAA": 1925, "GAATGTG": 1926, "CTTATTA": 1927, "GCACTGA": 1928, "TGAGGTT": 1929, "CATCAAA": 1930, "CTTCTCC": 1931, "GTTTATG": 1932, "CTTTCCA": 1933, "GTGCCTG": 1934, "GAAAGGA": 1935, "GCATCTG": 1936, "TACCCA": 1937, "TAACAGA": 1938, "AAAAAAAAAAA": 1939, "CTATGAA": 1940, "CAGTAAA": 1941, "TAGCTA": 1942, "TCGTTTT": 1943, "GTGTCTT": 1944, "GAGCAAA": 1945, "TCTAAAAA": 1946, "GTTCACA": 1947, "GAAATGA": 1948, "CAAATGA": 1949, "GCCCTGA": 1950, "GTGTTTA": 1951, "TCATGTG": 1952, "CATATTA": 1953, "TCAAAAAAA": 1954, "TAAGTTA": 1955, "TCTCTCTT": 1956, "CCAGTGA": 1957, "CCTCTGA": 1958, "CAAGATG": 1959, "GCCTGTT": 1960, "GTTTGGG": 1961, "CATTCATT": 1962, "GCCCCTG": 1963, "GTTCTGA": 1964, "GCGGCC": 1965, "GCGGTT": 1966, "CAAAACAAAA": 1967, "TACATATA": 1968, "GAATTAAA": 1969, "TCAAGAA": 1970, "CTGTATT": 1971, "TTTTTATT": 1972, "GATTATT": 1973, "TCTAATG": 1974, "GTTGCTG": 1975, "TGAATGAA": 1976, "TCAGCTG": 1977, "CTTGATT": 1978, "CAGAATG": 1979, "CTAATTA": 1980, "TATAATG": 1981, "GTTTTGTTTT": 1982, "CCAGCCTG": 1983, "TGATGGA": 1984, "GCAGATT": 1985, "CTCTATT": 1986, "GCAGTCA": 1987, "TAAGTGA": 1988, "CTACACA": 1989, "CGCATG": 1990, "TAGCCA": 1991, "GTGGCTCA": 1992, "CAAATAAA": 1993, "GTGCTCA": 1994, "TTTTTTTTTT": 1995, "TAACATG": 1996, "TCCCAGCTA": 1997, "CAAAGTA": 1998, "TCATATA": 1999, "CAGCATG": 2000, "TGATCTT": 2001, "CATAATT": 2002, "TGTGTTA": 2003, "TTTTGAA": 2004, "TTAATTA": 2005, "GATATTA": 2006, "TCATTCA": 2007, "TGATATA": 2008, "TGACTCA": 2009, "GACGTT": 2010, "TGACATG": 2011, "GTTGTGA": 2012, "CATTTTTT": 2013, "GCCTGGA": 2014, "CTATGTT": 2015, "CTTTGGG": 2016, "GTCTCAAA": 2017, "CTGGCTG": 2018, "CCACATG": 2019, "GGCGTG": 2020, "CTTAATG": 2021, "TAAGATG": 2022, "GTATAAA": 2023, "TGTATTA": 2024, "TAACTCA": 2025, "GAGAGAGAGAGAGAGAGAGAGAGAGAGAGAGA": 2026, "GCATGAA": 2027, "GTTAATG": 2028, "TCCAGGA": 2029, "GAGAGAAA": 2030, "TCTCTGTG": 2031, "CTCTCTA": 2032, "CCACCTG": 2033, "GCCAGGA": 2034, "CTGGAGG": 2035, "CCATTTA": 2036, "GTCTGGA": 2037, "GCCCACA": 2038, "TAGAGAA": 2039, "CAACTCA": 2040, "GGCAGGA": 2041, "TCTTATG": 2042, "CAAAGGA": 2043, "GGTAAAA": 2044, "GAGAGGA": 2045, "GTCCAGA": 2046, "GCCCTCA": 2047, "GATATTTT": 2048, "CAGGGAA": 2049, "CCACATT": 2050, "GAGGAGG": 2051, "GAAACTT": 2052, "CAGAATT": 2053, "TCAGATG": 2054, "TATTTCC": 2055, "TACAGTG": 2056, "TGAGCTG": 2057, "CCATCTG": 2058, "GAGAATG": 2059, "TCAACAA": 2060, "ATT": 2061, "TAACTGA": 2062, "TGAGAGG": 2063, "CACTGAA": 2064, "CCACCTT": 2065, "CTGCAGA": 2066, "TCACCAA": 2067, "TGAGCTT": 2068, "CAAAGCA": 2069, "GGTTTTA": 2070, "CGGGGTT": 2071, "TCCAAAAA": 2072, "TATGTATA": 2073, "CCAGATG": 2074, "TCCATTTT": 2075, "CTGCTCA": 2076, "GATAATT": 2077, "CCACCAA": 2078, "CTCCTCC": 2079, "GAGAATT": 2080, "GAAAGTA": 2081, "TAAAATAAAA": 2082, "CTTCTTA": 2083, "CTGTTTA": 2084, "GAATCAA": 2085, "GCATGTT": 2086, "GCACGG": 2087, "GACTGAA": 2088, "GTGCACA": 2089, "GACGTG": 2090, "TATACAA": 2091, "TCGACA": 2092, "GAAGACA": 2093, "TAAAGGA": 2094, "GATCAAA": 2095, "CAGTGTG": 2096, "CTAGCC": 2097, "GAGGAAAA": 2098, "TCTGAAAA": 2099, "GAACCCA": 2100, "GATGGATG": 2101, "GTTCTTA": 2102, "CTATATT": 2103, "GCATTAA": 2104, "TCTCTCTCTCTCTCTCTCTCTCTCTCTCTCTC": 2105, "TCAGTC": 2106, "TATTTTTG": 2107, "GAGGATT": 2108, "GTATGTG": 2109, "TAACCAA": 2110, "GTTGTTTT": 2111, "TTTTTCTT": 2112, "GTGTTAA": 2113, "CTTGGAA": 2114, "AAAAAATG": 2115, "CAATGTG": 2116, "GTGCCTT": 2117, "GCCTCAA": 2118, "GAGTCTT": 2119, "GCTAATTTT": 2120, "CGAAAAA": 2121, "GTGTATA": 2122, "GCGTTA": 2123, "CTGCACTCCAGCCTGGG": 2124, "GTTCATG": 2125, "CAAAGAAA": 2126, "GCAGTAA": 2127, "GGATGAA": 2128, "CTTTATG": 2129, "CAGGAAAA": 2130, "TCCTGCA": 2131, "CTGTCTG": 2132, "GAACATG": 2133, "GGATGGA": 2134, "GCCTGAA": 2135, "CAAAAATG": 2136, "TCCAATG": 2137, "CCAGCAA": 2138, "GGCCTA": 2139, "CAACTGA": 2140, "GCACCTG": 2141, "GTCTATT": 2142, "CCTCTCA": 2143, "GTGGTCA": 2144, "GTGTAAA": 2145, "GTACACA": 2146, "GTAAAATT": 2147, "GTACATT": 2148, "TATATAAA": 2149, "CTGTTAA": 2150, "TAAGTCA": 2151, "GCCTCCA": 2152, "AAATTAAA": 2153, "GTGCAGG": 2154, "TCCTGGA": 2155, "GTGCAAA": 2156, "GCGTCC": 2157, "CCATTAA": 2158, "GGAGGGA": 2159, "TCACTTA": 2160, "TCATTAAA": 2161, "CAACATA": 2162, "TAATAGA": 2163, "TAATGTA": 2164, "GATTTTTT": 2165, "GTTGTCA": 2166, "GGAGACA": 2167, "GTGTGGG": 2168, "TCACAGG": 2169, "TCGGCA": 2170, "CTCCCTG": 2171, "GACCAAA": 2172, "TGTTTATT": 2173, "CGAATG": 2174, "CTCAATG": 2175, "TCACCTG": 2176, "CAGTGTT": 2177, "TGAGACA": 2178, "TAGGGG": 2179, "GAAAAATG": 2180, "GTTGAGA": 2181, "TCGATA": 2182, "CTCGGGAGG": 2183, "GTTGTC": 2184, "CCAGTCA": 2185, "GCCCAGGCTG": 2186, "GAACAGA": 2187, "GGCTCACTGCAA": 2188, "GCAGACA": 2189, "TGAGGTG": 2190, "CACGTT": 2191, "TAAGAAAA": 2192, "CCAGGCA": 2193, "GTATCTT": 2194, "CTTGGGAGG": 2195, "CTTTCTA": 2196, "CCGCTG": 2197, "GAGCTCA": 2198, "GAGACAGA": 2199, "CTTCAGG": 2200, "GCACATT": 2201, "GTACAAA": 2202, "CTTGTAA": 2203, "GTGGGTG": 2204, "GAAGTGA": 2205, "GGTCTC": 2206, "GTATGTT": 2207, "GCACTCA": 2208, "TTATGTT": 2209, "CAAGTCA": 2210, "CAAGTGA": 2211, "GAAACTA": 2212, "TAAATAAAA": 2213, "TCTTAAAA": 2214, "GTTGGAA": 2215, "GTTCTAA": 2216, "CCACTC": 2217, "CAGTGAA": 2218, "GAAAGG": 2219, "GCACGA": 2220, "TAACTTTT": 2221, "GTTGTTA": 2222, "TCAGTTA": 2223, "CGGATG": 2224, "TATTTGAA": 2225, "CCCTGAA": 2226, "GCCCTC": 2227, "CTTCTAA": 2228, "TTTGTTTT": 2229, "GAGCTGA": 2230, "CTGTGGG": 2231, "CAAGATT": 2232, "GAAGCTT": 2233, "TGAGTAA": 2234, "CTTGCTG": 2235, "GGATGGG": 2236, "CGTATG": 2237, "TCCATTA": 2238, "GTCTGCA": 2239, "GCCATTTT": 2240, "GTTGTAA": 2241, "CACACAA": 2242, "GGACTACAGG": 2243, "CGTTTTA": 2244, "TCTTCC": 2245, "TAACCTT": 2246, "CTTTAAAA": 2247, "TGAATTTT": 2248, "CTACAGA": 2249, "GCAAGAA": 2250, "TAACAAAA": 2251, "CAATTAAA": 2252, "CCACTCA": 2253, "CATGGTGAAA": 2254, "CCCAGAA": 2255, "CTACATT": 2256, "CCGAGG": 2257, "TCCAGTG": 2258, "TGAGTTA": 2259, "GGAGTCA": 2260, "TAACGA": 2261, "GAGTAAA": 2262, "GACTCTG": 2263, "GGAGCTT": 2264, "TACTCC": 2265, "CTGCATG": 2266, "GCTTTTTT": 2267, "GTCTAAA": 2268, "GTGCGG": 2269, "CATCTCA": 2270, "TGATCAA": 2271, "GGAGATT": 2272, "GCAAAAAA": 2273, "CACCAAA": 2274, "TGACGG": 2275, "CAGAGG": 2276, "GTTGATG": 2277, "CTTGTCA": 2278, "TCCACCTG": 2279, "GGAGCAA": 2280, "CAAGTAA": 2281, "CCATAAA": 2282, "GTGCATG": 2283, "GCATATT": 2284, "GTAGATT": 2285, "GCCTAA": 2286, "CTCAAAAA": 2287, "GGAGAAAA": 2288, "CTATCC": 2289, "TAATATTA": 2290, "GTGCTC": 2291, "CAATATG": 2292, "TGTGGAA": 2293, "TGACTC": 2294, "GTGTATG": 2295, "TTTTAATG": 2296, "GCTCTAA": 2297, "CACAATG": 2298, "CAGCTCA": 2299, "GTTGGTT": 2300, "CTAAAATT": 2301, "GTCTATG": 2302, "TGTGAAAA": 2303, "CTGGGTT": 2304, "CCCCTCC": 2305, "CCCTCTT": 2306, "GCAGGGA": 2307, "GAAACCA": 2308, "CATTTCC": 2309, "GCAGCCA": 2310, "TCATATG": 2311, "GCAGGCA": 2312, "CGTAAAA": 2313, "TGACCTG": 2314, "CAGAGGTT": 2315, "CTTGTGA": 2316, "TTATCTT": 2317, "CTGTATG": 2318, "GTCAATG": 2319, "GGACGG": 2320, "GCGTAA": 2321, "CAAACTA": 2322, "TAAATGTT": 2323, "CTTCGG": 2324, "CTCCCCA": 2325, "TACAATG": 2326, "TCTGTAA": 2327, "GAATATG": 2328, "GCGGGA": 2329, "GGACATT": 2330, "TTATGAA": 2331, "GGATGTT": 2332, "GGACATG": 2333, "TCAGGTG": 2334, "CAACAAAA": 2335, "GAAAGAGA": 2336, "GTGGATG": 2337, "GGGCTA": 2338, "CCATCAA": 2339, "CAGCTGA": 2340, "CTCCACC": 2341, "CAATCAA": 2342, "GTGGTC": 2343, "TGACAGG": 2344, "CCATTCA": 2345, "GTCCCTG": 2346, "CAGACACA": 2347, "GTTGGTG": 2348, "CCTCCTG": 2349, "GAACTGA": 2350, "TATTCATT": 2351, "GCCCATG": 2352, "CAATCTT": 2353, "GAAAGCA": 2354, "GAATCTG": 2355, "TTATTTTA": 2356, "GTTTGGA": 2357, "TTTTTGTT": 2358, "GGGAATG": 2359, "GCGACA": 2360, "TAAACTG": 2361, "CCATATT": 2362, "GGATCC": 2363, "CAAGCTT": 2364, "TAAAAAAAAA": 2365, "TCACTC": 2366, "CACTGTT": 2367, "TGTTAATT": 2368, "GGACTGA": 2369, "GGAGTGA": 2370, "CATACACA": 2371, "GTTTGTA": 2372, "TCCAGCA": 2373, "GTGCATT": 2374, "GGAAAAAA": 2375, "CCAAGAA": 2376, "TCAATA": 2377, "CTTCCCA": 2378, "TGAGAAAA": 2379, "GGCCTCCCAAA": 2380, "CAAGCTG": 2381, "GCCCAAA": 2382, "TGACTTA": 2383, "CAGCCTT": 2384, "CTGGATT": 2385, "TTTTTTTA": 2386, "TCACGG": 2387, "GCAGTTA": 2388, "TGACTAA": 2389, "TTACAGG": 2390, "TGATATG": 2391, "TAATTATT": 2392, "TCTTGAA": 2393, "GCCCCTT": 2394, "GTTCAGA": 2395, "CTCTATG": 2396, "CCATGGA": 2397, "GAGGGAA": 2398, "GGAGGCA": 2399, "CTTTGCA": 2400, "TCTTGG": 2401, "GGAGGTT": 2402, "GCCAATG": 2403, "CTGGTGA": 2404, "CAACCAA": 2405, "CCAGTC": 2406, "CTTGAGA": 2407, "TACAGCA": 2408, "CTTGTC": 2409, "GACGGA": 2410, "CTTCTTTT": 2411, "GTGGC": 2412, "GAGGATG": 2413, "CAATAAAA": 2414, "GAAATTTT": 2415, "AAAAAAAAAA": 2416, "CTCTATA": 2417, "GTATGAA": 2418, "CTTGTTA": 2419, "TAACATA": 2420, "CAAACACA": 2421, "TGATTAAA": 2422, "GCTCTGTT": 2423, "GTGGGTT": 2424, "GTTGGGG": 2425, "GTGTGTA": 2426, "GTAATTTT": 2427, "GTATCC": 2428, "TGTGTGTGTGTG": 2429, "TCTTCCTT": 2430, "TCACTAA": 2431, "TCTCCAAA": 2432, "TATCAAA": 2433, "TGATGGG": 2434, "GGATATT": 2435, "CAAATTTT": 2436, "GTTCAGG": 2437, "GTGGATT": 2438, "GTGCAGA": 2439, "GCTGCC": 2440, "CTCAGAA": 2441, "GCAGTC": 2442, "GGATAAA": 2443, "GCCTTCA": 2444, "CCAGGTG": 2445, "TATCTC": 2446, "CAATGCA": 2447, "CCCACTG": 2448, "GTGTATT": 2449, "CGACAGA": 2450, "TGAGATA": 2451, "CCAGGTT": 2452, "TGTTTAA": 2453, "CATCATG": 2454, "TGATTCA": 2455, "GCAATTA": 2456, "GAAATGAA": 2457, "CTTGGTT": 2458, "GAAGATT": 2459, "GGATTAA": 2460, "CCTCATT": 2461, "GGCCAGGCTG": 2462, "GCTATTA": 2463, "GCCAGCA": 2464, "GAGACAGG": 2465, "CTTGAGG": 2466, "CAGTCTT": 2467, "GTTCTCC": 2468, "TATTTCAA": 2469, "TGACGA": 2470, "CATGAAAA": 2471, "CATTATG": 2472, "TAAATTTA": 2473, "GAGTGAA": 2474, "CAACAGG": 2475, "TAAGCTT": 2476, "CACATTTT": 2477, "GATCTCA": 2478, "TAGTCC": 2479, "GACCCTG": 2480, "TAATGCA": 2481, "TAAGTC": 2482, "TAATAATT": 2483, "GAAGTAA": 2484, "CAACTC": 2485, "CATCATT": 2486, "GACGAA": 2487, "GAAACAAA": 2488, "TATTTCTG": 2489, "CATTAATT": 2490, "CCACCCC": 2491, "TAATATTTT": 2492, "GTTTAAAA": 2493, "GTATCTG": 2494, "GTCAAAAA": 2495, "GATGCTG": 2496, "TGTTCTG": 2497, "GGTCAAA": 2498, "GTAGGAA": 2499, "GTATATG": 2500, "TGATCTG": 2501, "GGGGCTG": 2502, "GCATCAA": 2503, "GCCAAAAA": 2504, "CCACGA": 2505, "GCTAATG": 2506, "CAGAGAAA": 2507, "CCTTCTG": 2508, "TCCTCTA": 2509, "GCAGGTT": 2510, "CTCACTG": 2511, "TAGATTA": 2512, "GCCGAGA": 2513, "CCATCCA": 2514, "CTTTACA": 2515, "GTACATG": 2516, "GCACCAA": 2517, "CTTTGTA": 2518, "CTATGTG": 2519, "TCACTTTT": 2520, "TGAGTC": 2521, "CAAGAAAA": 2522, "CTGACTG": 2523, "GTTTTTTTT": 2524, "GCATAAA": 2525, "TAATCTG": 2526, "GAAAAAAAA": 2527, "CAGGATG": 2528, "TGAGCCA": 2529, "GAATTCA": 2530, "TCAGACA": 2531, "GTTCCAA": 2532, "TCAGGTT": 2533, "CAAACTG": 2534, "CATTTCTT": 2535, "TGTTAAAA": 2536, "CCAGACA": 2537, "CAAGTTA": 2538, "CATGTTA": 2539, "CATTCTA": 2540, "TCTTTTTG": 2541, "TGAGGGG": 2542, "CACATTA": 2543, "TAAAATAAA": 2544, "GCATATA": 2545, "TGTTCTA": 2546, "GAAGGGG": 2547, "GAGTGTG": 2548, "TAAGACA": 2549, "GAACTC": 2550, "CCAGTAA": 2551, "GAGAGAGG": 2552, "GCGACC": 2553, "CAATTCA": 2554, "CGGCTG": 2555, "CCAGATT": 2556, "CCTGGG": 2557, "GGAAGAAA": 2558, "GAGAGG": 2559, "TCAAAATG": 2560, "CCTCATG": 2561, "TAAAGG": 2562, "CTTTGGA": 2563, "CCAGGGA": 2564, "GTACAGA": 2565, "CTGAGGCAGGA": 2566, "TGTTTCTT": 2567, "CCAGGCTG": 2568, "CTGAGG": 2569, "GAGGCTG": 2570, "CTCCTGGG": 2571, "GAAGTC": 2572, "CGACC": 2573, "GGACTCA": 2574, "GGAGTC": 2575, "CACAATT": 2576, "GTGTTCA": 2577, "GACTAAA": 2578, "GTCATTA": 2579, "CAAAATTA": 2580, "TGAAGAAA": 2581, "GCACCTT": 2582, "GTTTGCA": 2583, "TCCTGCC": 2584, "GTAGATG": 2585, "GCCTGCA": 2586, "GAGTTAA": 2587, "TCCCTTA": 2588, "GTGGTTA": 2589, "TCGGGA": 2590, "TACATAA": 2591, "TCTCTCCA": 2592, "CACTAAA": 2593, "TATATATATATA": 2594, "GTGGCAA": 2595, "CACCATG": 2596, "TTTGAAAA": 2597, "CACACTG": 2598, "CTTGGTG": 2599, "TACACTG": 2600, "CCTCCAA": 2601, "CAACCTT": 2602, "CAGCCAA": 2603, "TTTTCAAA": 2604, "TGATAGA": 2605, "TACACTA": 2606, "TCTGGG": 2607, "TCCCAGCA": 2608, "TAGGAAAA": 2609, "CTTGGGG": 2610, "TCTGTGAA": 2611, "CCTTATT": 2612, "CATTTAAA": 2613, "TTTTATTTTA": 2614, "GCCCTCC": 2615, "CTGAGCA": 2616, "CCCGTG": 2617, "GTAGTGA": 2618, "TCCTATT": 2619, "GAAGGTG": 2620, "TGTGCTG": 2621, "TCCACTG": 2622, "TAATCTA": 2623, "TGATGTA": 2624, "GTGGTAA": 2625, "TAATGGA": 2626, "GATGAAAA": 2627, "GTAGTAA": 2628, "GTGGGGA": 2629, "GTGTCAA": 2630, "CAGACTG": 2631, "TCGAAAA": 2632, "CTCATTA": 2633, "TAATAATA": 2634, "CTCAGAAA": 2635, "CATCCTT": 2636, "CCGCTT": 2637, "GGAAGG": 2638, "CCGTGA": 2639, "CCACTCC": 2640, "CTAGAGA": 2641, "TAGAATG": 2642, "GGATTTA": 2643, "TTAATTTT": 2644, "GCTAATA": 2645, "TCCCCCA": 2646, "CAAATATT": 2647, "GATCATG": 2648, "TCTTAATT": 2649, "CAGTATT": 2650, "GTCTTGAA": 2651, "CCGAAA": 2652, "CTATTCA": 2653, "TAAGATA": 2654, "CTTGCAA": 2655, "GCCCCAA": 2656, "TCCCTAA": 2657, "GAAGTTA": 2658, "GATGATG": 2659, "CTTGATG": 2660, "CCCTAAA": 2661, "CCTGCCTG": 2662, "GACATTTT": 2663, "CCAGCCA": 2664, "TGTGTGTGTG": 2665, "GTCTATA": 2666, "TCTCTGTT": 2667, "GTCTGTA": 2668, "TATAATA": 2669, "CTTGTTTT": 2670, "CGCCATT": 2671, "CTCAGCA": 2672, "TACAGTT": 2673, "CAAGAGG": 2674, "GGAAGCA": 2675, "GCCTTTA": 2676, "CCCCATT": 2677, "CAACGA": 2678, "GTCATTTT": 2679, "CCCGCA": 2680, "CAGTTAA": 2681, "GAATCTT": 2682, "CATGTTTT": 2683, "CCGGGG": 2684, "CTACTGA": 2685, "TCACGA": 2686, "TAAATTTG": 2687, "GCCCATT": 2688, "CTCTAGG": 2689, "GGACCTG": 2690, "TCAGGGA": 2691, "GAGACTG": 2692, "CCAAAAAA": 2693, "GCCGG": 2694, "CCAGGGG": 2695, "TCAGAAAA": 2696, "CATCTGA": 2697, "TCTTCAAA": 2698, "CTACAGG": 2699, "GAGGCAGG": 2700, "CATTGTA": 2701, "TAAATCAA": 2702, "GACTCTT": 2703, "CTGATTA": 2704, "GCATATG": 2705, "GGACCTT": 2706, "CAAGACA": 2707, "TATTTATG": 2708, "TATTTTAAA": 2709, "CCGAGA": 2710, "TCATTTTA": 2711, "CTCACTCA": 2712, "CCACCCA": 2713, "CTCTAGA": 2714, "CTACATG": 2715, "GTGCTTA": 2716, "CAACCTG": 2717, "TCTGTGTT": 2718, "TAAATATG": 2719, "CAAAGG": 2720, "CCCTGTT": 2721, "GTTCGG": 2722, "TGATAAAA": 2723, "CACGAA": 2724, "GTTGAGG": 2725, "CAGAGTGA": 2726, "GAAATTAA": 2727, "CACATA": 2728, "GAACAGG": 2729, "TCTCCTGA": 2730, "CCTGAGG": 2731, "GGAGGCCAA": 2732, "GTTTACA": 2733, "TAACAGG": 2734, "TGTGGTG": 2735, "GCCTCCCAAA": 2736, "CCATCCTG": 2737, "GATTCTT": 2738, "GAATGGA": 2739, "GTAGTCA": 2740, "CTCCTCTG": 2741, "GAAAGAAAGAAAGAAA": 2742, "CCCTGTG": 2743, "CAGTATG": 2744, "GCGATA": 2745, "GGACTC": 2746, "GAAAGA": 2747, "TGTTGG": 2748, "GTAGCTT": 2749, "CATTTTAA": 2750, "CCCTCTG": 2751, "GCATTCA": 2752, "CGATTA": 2753, "TCACATA": 2754, "TAATGAAA": 2755, "GGAATTA": 2756, "CTGTCAA": 2757, "TAAATTAAA": 2758, "CAAGTC": 2759, "GTATTCA": 2760, "GGCCATG": 2761, "CTTTAGA": 2762, "TGTTTCC": 2763, "CATGTA": 2764, "GAATAAAA": 2765, "CAACTAA": 2766, "TCATCTA": 2767, "CACTCTT": 2768, "CAGTTTG": 2769, "CATAAAAA": 2770, "GCATGCA": 2771, "GATTTA": 2772, "GAACCAA": 2773, "TCTGTGA": 2774, "TCAGCCA": 2775, "TCTCCACA": 2776, "TCTCAGCTCA": 2777, "TATCATG": 2778, "GCACTTA": 2779, "CGCCAGG": 2780, "CGGGG": 2781, "CATTAAAAA": 2782, "TTTGTTA": 2783, "GGATATA": 2784, "TCGACC": 2785, "TAATCCA": 2786, "CCGC": 2787, "CATTGTT": 2788, "CCAGTTA": 2789, "GTAGTTA": 2790, "CTAGGAA": 2791, "CCTAATT": 2792, "TCATGGG": 2793, "GAACTAA": 2794, "GCTATTTT": 2795, "CCGTCA": 2796, "CAGATTA": 2797, "CCATATA": 2798, "CAACTTA": 2799, "TCAGTTTT": 2800, "CTACCTT": 2801, "GCACTC": 2802, "GTGTGGA": 2803, "GTGCCAA": 2804, "GACAATG": 2805, "GACAATT": 2806, "GTACCTT": 2807, "TAAACATT": 2808, "CAGGAGG": 2809, "GTGCGA": 2810, "GAAAATTA": 2811, "TCTCTTAA": 2812, "CCGATT": 2813, "GATGATT": 2814, "CCATGGG": 2815, "TCGGTA": 2816, "CCATATG": 2817, "CCAGTCC": 2818, "GCCTTAA": 2819, "TGATCCA": 2820, "GTTGCAA": 2821, "GTAGAGG": 2822, "CAGATTTT": 2823, "GTACTTA": 2824, "TCTTTCTTTCTTTCTT": 2825, "GCTCTGTG": 2826, "TCAATAA": 2827, "GTTTAGA": 2828, "GTTCGA": 2829, "CAAGGTT": 2830, "CTCATTTT": 2831, "CACAGG": 2832, "CATGCTG": 2833, "GAACGG": 2834, "TATAAAAA": 2835, "GAAGGCA": 2836, "GAGCATT": 2837, "TGTTTGTG": 2838, "GCTGTTA": 2839, "GTCACTG": 2840, "CAAATGAA": 2841, "GTGACTG": 2842, "GTTCTTTT": 2843, "CAGGCTGGAGTGCAGTG": 2844, "TGATGAAA": 2845, "TAACGG": 2846, "CTACTAA": 2847, "GACATTA": 2848, "GGACGA": 2849, "GAGCATG": 2850, "GCATGGG": 2851, "CCACTTA": 2852, "CTATCAA": 2853, "GCTGTTTT": 2854, "GTCGTG": 2855, "CCTGGCC": 2856, "TCTCTGAA": 2857, "TGTTGTA": 2858, "CAGCCAGG": 2859, "GTTTAGG": 2860, "CCGCAA": 2861, "GGAGTAA": 2862, "CCAATTA": 2863, "CAGCAAAA": 2864, "TCATCCA": 2865, "CACGTA": 2866, "TCATAGA": 2867, "TAATTAAAA": 2868, "CACTTAA": 2869, "TCTTTATT": 2870, "GAGATTA": 2871, "TAAGAGG": 2872, "CAAATTAA": 2873, "GACGCA": 2874, "CACGGA": 2875, "GTGTGCA": 2876, "TCT": 2877, "TATTATTA": 2878, "GAAATATT": 2879, "GGAGTTA": 2880, "TCTTTGA": 2881, "CTGATTTT": 2882, "TGTGAATT": 2883, "TCCCACC": 2884, "CCCTTTG": 2885, "CAAGGTG": 2886, "CAGAGTT": 2887, "CCCCATG": 2888, "CTACCAA": 2889, "CTCCAAAA": 2890, "CTTCCCC": 2891, "CTGCTAA": 2892, "GATTAAAA": 2893, "GCTTATG": 2894, "CTACTTA": 2895, "TAAAAAATT": 2896, "TCAGTCC": 2897, "CTATTAAA": 2898, "GAATGGG": 2899, "CACAGTA": 2900, "CAACGG": 2901, "GGTTATT": 2902, "TCACCCA": 2903, "TGATGCA": 2904, "TAATTTTTT": 2905, "GTTTGAGA": 2906, "GTATTAAA": 2907, "GCCCCCA": 2908, "TATAGTA": 2909, "TAGTAAA": 2910, "TGATACA": 2911, "GTGGTTTT": 2912, "CCACTAA": 2913, "CACAGAGA": 2914, "CCTCTGCCTCC": 2915, "CAAAAAAAA": 2916, "CTCTCTCC": 2917, "CATAATA": 2918, "GAAGCCA": 2919, "GTTCCCA": 2920, "TGTGTTTG": 2921, "CAATGGA": 2922, "TGAAGTA": 2923, "CTTCATA": 2924, "CACTGTG": 2925, "GCTCTTTT": 2926, "TGACATA": 2927, "TAAAGAAAA": 2928, "GAGAAATG": 2929, "CAGGGAGG": 2930, "TGTTCAA": 2931, "GAGCCAA": 2932, "GACAGAGA": 2933, "GGCTGAA": 2934, "CAAATATA": 2935, "GTGGAAAA": 2936, "TAAGGTT": 2937, "GTGATTA": 2938, "GGATCTG": 2939, "GATGTTA": 2940, "GACTACACA": 2941, "TCCTATA": 2942, "CTGCCAA": 2943, "TCCCGA": 2944, "GTGATTTT": 2945, "GCGTTTT": 2946, "CAGAGTA": 2947, "GAAAGGAA": 2948, "CACTTTG": 2949, "CCCCAAAA": 2950, "GCAACCCA": 2951, "TGCATTTT": 2952, "TCTAGAA": 2953, "TACTTTG": 2954, "TGAGGCA": 2955, "CATCTCC": 2956, "TCGCTA": 2957, "TGACTTTT": 2958, "GAGCCTG": 2959, "CATTTGTT": 2960, "TCTTTGTT": 2961, "GCAAAATT": 2962, "CCTGATT": 2963, "GATAAAAA": 2964, "GAGTGTT": 2965, "TCCTGTA": 2966, "TACAGAAA": 2967, "TCCAGGAA": 2968, "GCCAGTG": 2969, "TAGATTTT": 2970, "TAATAGG": 2971, "CTCCTCA": 2972, "CATTTTTG": 2973, "CATTTCAA": 2974, "GCCATCA": 2975, "TAAAATATA": 2976, "GACTGTT": 2977, "GCATGGA": 2978, "CAAAGTT": 2979, "CATGATT": 2980, "GAGTTTG": 2981, "CTAGCAA": 2982, "CTTCCTA": 2983, "GGGGAGG": 2984, "CTATATG": 2985, "TATTTATTTT": 2986, "CACCATT": 2987, "CCCTCAA": 2988, "TTTTTTTTTTTTTT": 2989, "GATCATT": 2990, "GTACATA": 2991, "CTCCATA": 2992, "CCCCGTCTCTA": 2993, "GCCTGCC": 2994, "CTAGCTT": 2995, "CCCGGA": 2996, "GATGTTTT": 2997, "GTATTTTA": 2998, "TCAGATA": 2999, "CCTGGAA": 3000, "TATTCCA": 3001, "GGACCAA": 3002, "GCCATTA": 3003, "CGACTGA": 3004, "TAAGCTG": 3005, "TAAACACA": 3006, "GTTTCTC": 3007, "CATCTTA": 3008, "GAAATTTG": 3009, "TAATGGG": 3010, "TAAAATTTT": 3011, "CTGTTCA": 3012, "CCTGTTA": 3013, "TACTGAA": 3014, "TGACCCA": 3015, "TGATTTTA": 3016, "CTCCTTA": 3017, "TATAGAA": 3018, "CTGCGG": 3019, "GCGGTA": 3020, "GTGCTAA": 3021, "CAGAGGAA": 3022, "TACATCA": 3023, "TCAATCAA": 3024, "CTGCAGCC": 3025, "TGAATATT": 3026, "TCTACAA": 3027, "CCACATA": 3028, "CCCGTT": 3029, "TATACACA": 3030, "TCCTCTC": 3031, "TCTACTT": 3032, "CCGGAA": 3033, "CTTTTTTA": 3034, "GAAAGAAAA": 3035, "CTATCTT": 3036, "GACTTTG": 3037, "TGAACAA": 3038, "GCAGTTTT": 3039, "GCTAAAAA": 3040, "GAGGCGG": 3041, "TAATAAAAA": 3042, "CTGGTCA": 3043, "CAGACAA": 3044, "GGATATG": 3045, "TGAAGG": 3046, "GCCAGAA": 3047, "CCAGGCC": 3048, "CCACCATG": 3049, "CAAACTT": 3050, "TCATGTA": 3051, "GCTGCTT": 3052, "GTAATA": 3053, "CCCCCAA": 3054, "CAGCCTG": 3055, "TCAACTT": 3056, "TAAAATTAA": 3057, "GCTGAAAA": 3058, "CGACGA": 3059, "GTGGGCA": 3060, "TGAGGGA": 3061, "CGCTCC": 3062, "TTTTGTTTT": 3063, "GAGTCAA": 3064, "TCATGCA": 3065, "CTGCTTA": 3066, "TAAGTTTT": 3067, "GTAGCAA": 3068, "CCTTGG": 3069, "TGACAAAA": 3070, "CTGGTAA": 3071, "TCTTTATA": 3072, "TGTGTGTT": 3073, "CTGGTC": 3074, "CTGGCAA": 3075, "CATTTCTG": 3076, "CTCTACC": 3077, "CTGAGGA": 3078, "CTAAAATG": 3079, "CTAGATT": 3080, "GTATCAA": 3081, "CAGTCAA": 3082, "CTGGGTG": 3083, "CCTCTTA": 3084, "TGAGTTTT": 3085, "TTTTATTTA": 3086, "CCTTTTTT": 3087, "TATATACA": 3088, "TAGCAAA": 3089, "AAATTA": 3090, "CTGGATG": 3091, "GATAATA": 3092, "GACAAAAA": 3093, "CCTGGGA": 3094, "GCTTTCA": 3095, "GTACAGG": 3096, "GCTGGAA": 3097, "CTACTCA": 3098, "CAATGTA": 3099, "GCGTGAA": 3100, "GATCCTT": 3101, "TATTAATG": 3102, "GCCCGA": 3103, "TAAAGTG": 3104, "GCTTCCA": 3105, "CATGGAA": 3106, "TGAAGTT": 3107, "CTTTCTC": 3108, "TCTGTGTG": 3109, "GTATGTA": 3110, "CAATACA": 3111, "TCAAGG": 3112, "CCTCTAA": 3113, "TGTGGG": 3114, "GATCTGA": 3115, "GTACTGA": 3116, "TTAATTAA": 3117, "GCAGAAAA": 3118, "CTACATA": 3119, "CCGGTG": 3120, "GGGGAAAA": 3121, "TACAAAAAA": 3122, "TTTTGG": 3123, "GTGAGAA": 3124, "TCAATAAA": 3125, "TCAAGTT": 3126, "CTCAGGA": 3127, "CTACTC": 3128, "CAAATCA": 3129, "GGCAGAA": 3130, "CCCGAA": 3131, "TGTTGTG": 3132, "GAGCAAAA": 3133, "TATTTGTG": 3134, "GTAGGTT": 3135, "CTACCTG": 3136, "CACAAAAA": 3137, "CTCAGG": 3138, "GCTTTA": 3139, "CAGAGCAA": 3140, "CTCAGTG": 3141, "GGAAGAGA": 3142, "TAACCTG": 3143, "GAAATATA": 3144, "CGAGAA": 3145, "GTGAGG": 3146, "CATTTATA": 3147, "GGCAGCA": 3148, "TCTAAATT": 3149, "CCCAGTG": 3150, "GCCTAGG": 3151, "TGCATTA": 3152, "CCGTAA": 3153, "CATTCCA": 3154, "CTAGTTA": 3155, "GACTTAA": 3156, "CTATACA": 3157, "GACACAA": 3158, "TCTTCACA": 3159, "CCGGTT": 3160, "TAAAGTAA": 3161, "CTGTGGA": 3162, "TAAGGTG": 3163, "TCCAGTA": 3164, "CAAATTTA": 3165, "AAATTAAAA": 3166, "CCATCTA": 3167, "CTCCCTT": 3168, "CTCCTTTT": 3169, "GAGAGAGAGAGA": 3170, "GGAGATA": 3171, "CCTATTA": 3172, "CACCAAAA": 3173, "CCGTTA": 3174, "TGTTTATA": 3175, "CTCAGGAGG": 3176, "GACGTA": 3177, "GTCCTTA": 3178, "GAAAGTT": 3179, "GCTGGTG": 3180, "CTCTACA": 3181, "CAATAGA": 3182, "TAAAATATT": 3183, "GTACCTG": 3184, "GTACTAA": 3185, "CTTTGAAA": 3186, "CCTTTCC": 3187, "TAAAAATTA": 3188, "CTCGG": 3189, "CAAGATA": 3190, "CATTTGA": 3191, "CACCTCA": 3192, "GCCAGCC": 3193, "GTCGG": 3194, "GCACATA": 3195, "CACTCAA": 3196, "CTTTTAAAA": 3197, "CAGGAATT": 3198, "GCCTATT": 3199, "TCTTTCTG": 3200, "CTGAGGCAGGAGAA": 3201, "CAGGCAGG": 3202, "CTAGTAA": 3203, "TCCATA": 3204, "GAACTTA": 3205, "CG": 3206, "GCTGTGA": 3207, "GAAAATA": 3208, "TCTTCATT": 3209, "GAGGGAGA": 3210, "CCCATCC": 3211, "GAGGTGGG": 3212, "GCCTCTA": 3213, "GTAGGTG": 3214, "TAAACCA": 3215, "GAAGGAAA": 3216, "TATTGG": 3217, "ATG": 3218, "TCCAGTT": 3219, "CCCACAA": 3220, "GAAACACA": 3221, "GTCTCAAAA": 3222, "CTTTTCTTTT": 3223, "TGAAGGA": 3224, "TATTGATT": 3225, "CTATGTA": 3226, "AAAAAAAAAAAAAA": 3227, "TCCTTAAA": 3228, "GCGCTA": 3229, "TCCACTT": 3230, "GACTCAA": 3231, "TAAATACA": 3232, "TCATGGA": 3233, "TCTGGGA": 3234, "TCCTATG": 3235, "CTGTGCA": 3236, "TCAAGTGA": 3237, "TCATAAAA": 3238, "CATCCAA": 3239, "CCTTCCA": 3240, "CTGTACA": 3241, "GAAGGTT": 3242, "CTGTGTA": 3243, "GTCACTT": 3244, "TCACAAAA": 3245, "TCAGGCA": 3246, "GTGTTAAA": 3247, "CCCTTAA": 3248, "CAAAGTG": 3249, "GAAATGTT": 3250, "CTGGGGA": 3251, "GACGCC": 3252, "TATATGTG": 3253, "CTAGATG": 3254, "GAAATTAAA": 3255, "GAATGCA": 3256, "GCACTAA": 3257, "CGGGAGG": 3258, "GCCACAA": 3259, "CGCTTA": 3260, "TCCACAA": 3261, "CAGATA": 3262, "TCTGAATT": 3263, "TATTATTTT": 3264, "GCGCGG": 3265, "CTCTGAAA": 3266, "TCTCTTTG": 3267, "TATTTCTA": 3268, "GGGGTGGG": 3269, "GGATGCA": 3270, "CCACACC": 3271, "TAAATGTG": 3272, "TCTTCCTG": 3273, "GCAAGG": 3274, "CTGCTCC": 3275, "CTGGAGTG": 3276, "CTGTTAAA": 3277, "CACACAAA": 3278, "CTGACTT": 3279, "GAAAAGAAAA": 3280, "CCTTCTCC": 3281, "GAAATAAAA": 3282, "CCTCAGGTGA": 3283, "GATAATG": 3284, "GAATTGCTT": 3285, "CCAAAATT": 3286, "CGTGAAA": 3287, "CACTGAAA": 3288, "CAGTGAAA": 3289, "GATCTTA": 3290, "GAGATGGG": 3291, "TCTGCCA": 3292, "TGAGGTA": 3293, "TATGGAA": 3294, "TATATTTTA": 3295, "TGAACTT": 3296, "GCAGATA": 3297, "CTTTTCTT": 3298, "GTAAAATG": 3299, "TCTCTAA": 3300, "TCTGCAAA": 3301, "GAGCCTT": 3302, "TATCATT": 3303, "CAATTTTA": 3304, "CCGCCA": 3305, "TATTTAAAA": 3306, "GAGAGATG": 3307, "GAGATGGA": 3308, "GCCAGGATG": 3309, "CGAGTAGCTG": 3310, "TTCATTTT": 3311, "TATACTT": 3312, "GTCTACA": 3313, "GTGAGTGA": 3314, "GCTACACA": 3315, "GGGAGGA": 3316, "CAAGGCA": 3317, "GCTTTTAA": 3318, "CACTATT": 3319, "GTTCATA": 3320, "TCCTC": 3321, "GTGGACA": 3322, "TATTTGGA": 3323, "CTCCAGTA": 3324, "GTTCAGTT": 3325, "CCAAGG": 3326, "CAGAGCC": 3327, "CTCGCC": 3328, "CCGATG": 3329, "GGAATTTT": 3330, "TCCAGCC": 3331, "CCTCTTTT": 3332, "GAACCTT": 3333, "CATGCACA": 3334, "GTTTC": 3335, "GAAGATA": 3336, "TACCCC": 3337, "GCTGCCA": 3338, "GGGGGAGG": 3339, "GCAGTGAGCTGA": 3340, "CTGTCTA": 3341, "CGAGGA": 3342, "CAATGGG": 3343, "GCTGTGAA": 3344, "GAAAGTG": 3345, "TACCAAAA": 3346, "GTCAGG": 3347, "CAGCTCC": 3348, "TGTGCTT": 3349, "GTCTAGG": 3350, "TTTTTGTA": 3351, "TTATATG": 3352, "TCAGGGG": 3353, "TATTGTTA": 3354, "CCTGAGA": 3355, "TATCTCA": 3356, "CAATCTG": 3357, "CACTCTG": 3358, "GATTTAA": 3359, "TGAATAA": 3360, "TCTTGTA": 3361, "TCAACTG": 3362, "TCTCCAGG": 3363, "CTAGAGG": 3364, "CTGAGAAA": 3365, "CTAGCTG": 3366, "TCCACCA": 3367, "CGATTTT": 3368, "CCGGCC": 3369, "GTTGACA": 3370, "CTTAGAA": 3371, "CATAATG": 3372, "GAGTATT": 3373, "CACAGAAA": 3374, "GACTGTG": 3375, "CTATTTTA": 3376, "TGAGGAAA": 3377, "TTATTAAAA": 3378, "CTTATTTA": 3379, "CAGACTT": 3380, "CACGCC": 3381, "GCTTGG": 3382, "CCTGCTT": 3383, "TAAAGCAA": 3384, "CCTCGTGA": 3385, "TAGAATT": 3386, "CTTACAA": 3387, "TAAAGGAA": 3388, "GTCTAGA": 3389, "GTGACTT": 3390, "TACATATG": 3391, "GTCAGGA": 3392, "GCTCCAGG": 3393, "GAAGGGA": 3394, "CATGATG": 3395, "TCATCAAA": 3396, "CGTTAAA": 3397, "GTACTCA": 3398, "CTCCCAA": 3399, "TATATGTA": 3400, "GGTATTTT": 3401, "TAAGCCA": 3402, "CGAAATT": 3403, "GTTTGTTTT": 3404, "TCTGTCTT": 3405, "TATATCA": 3406, "TGTTCATT": 3407, "CAAACCA": 3408, "TTCATTA": 3409, "TATTTGTA": 3410, "GATTGAA": 3411, "CTATAAAA": 3412, "GATTAATT": 3413, "CCCACCA": 3414, "TCCTAGG": 3415, "TAAATGTA": 3416, "CTCTTAAA": 3417, "GCAGTCC": 3418, "GCGGCTG": 3419, "GTCTCGAA": 3420, "TGAATGA": 3421, "CTGGGGG": 3422, "GTCTCGA": 3423, "GAACAAAA": 3424, "TGAATCA": 3425, "TGTATTTTTAGTAGAGA": 3426, "GTTATTAA": 3427, "TTTTTTAAAA": 3428, "GTCAGTG": 3429, "CCCATTA": 3430, "CACAGGA": 3431, "TATTCCTT": 3432, "TCTGCCTT": 3433, "CCTGGTG": 3434, "GCGAGC": 3435, "TACTAAA": 3436, "TACACAAA": 3437, "CCGTCC": 3438, "GCTTTGTT": 3439, "GCATCCA": 3440, "CATCTAA": 3441, "GCTGTGTT": 3442, "GTAGACA": 3443, "GCCTATG": 3444, "TCTTTGTG": 3445, "GATTCTG": 3446, "CGCCCGG": 3447, "GATGAGA": 3448, "TATCTGA": 3449, "TGAATTTG": 3450, "CCTGATG": 3451, "TAAAACAA": 3452, "CTTTAGG": 3453, "TTTTCCTT": 3454, "TGAATAAA": 3455, "CGGGGA": 3456, "CAAACATT": 3457, "GTATGGA": 3458, "GCTTAAAA": 3459, "TACCAAA": 3460, "CAAAGAGA": 3461, "CTCCTGCC": 3462, "GTAAAAAAA": 3463, "CACAGCC": 3464, "CCATGCA": 3465, "TACAATT": 3466, "CTAGTGA": 3467, "CTGAGTT": 3468, "GAGTGAAA": 3469, "TCTGTTTG": 3470, "CTGTAGG": 3471, "TATAAAAAA": 3472, "GCATTAAA": 3473, "GTCCATA": 3474, "TGTTAAAAA": 3475, "TGTTTGA": 3476, "GAATAGA": 3477, "CTTCAAAA": 3478, "CTGGACA": 3479, "CTGTAGA": 3480, "CCATTAAA": 3481, "CTATCTG": 3482, "CACTATG": 3483, "TTATCAA": 3484, "TAAGTAAA": 3485, "TAATCCCAGCACTTTGGGAGGCC": 3486, "CCAGAAAA": 3487, "TGAAGCA": 3488, "TCCCTTTT": 3489, "TCATACA": 3490, "TACGTT": 3491, "GCCGTG": 3492, "GGAAGTG": 3493, "GGCCAAA": 3494, "GTACCAA": 3495, "TCTCTACTAAAAATA": 3496, "CATTGTG": 3497, "TGTGTGA": 3498, "GAAACAGA": 3499, "CTTGACA": 3500, "GATGAGG": 3501, "GAGATTTT": 3502, "CCTTCAA": 3503, "GAATCTA": 3504, "CTCTCCTT": 3505, "GGCGGA": 3506, "TCTATCTATCTATCTA": 3507, "CACACAGA": 3508, "TGTGTGTA": 3509, "CAAAGCC": 3510, "TGTGCCA": 3511, "GTTGAAAA": 3512, "CTCCAGCA": 3513, "TCAAGGA": 3514, "TAGCTCA": 3515, "CGCTGA": 3516, "CCTGAAAA": 3517, "GACTATT": 3518, "GATTCCA": 3519, "GCTTCTA": 3520, "GTCTGCC": 3521, "CTTGGCA": 3522, "TGTGGTA": 3523, "GCTTTGA": 3524, "GCTCTCTG": 3525, "CTCACAGA": 3526, "TCTTTAAA": 3527, "CAAAGCAA": 3528, "TACTTAA": 3529, "GCTTCAA": 3530, "CATTGAA": 3531, "GGAGGAAA": 3532, "CTATAGA": 3533, "CTGAGGAA": 3534, "CCTGGCA": 3535, "CCCTATT": 3536, "CTCGTG": 3537, "TTACACA": 3538, "TTAGGAA": 3539, "CTGGTTA": 3540, "GTTGTCC": 3541, "TAATGAAAA": 3542, "TATTTACA": 3543, "GGGAATT": 3544, "GTAGTTTT": 3545, "GCTGCAA": 3546, "CTACGG": 3547, "GCCGGA": 3548, "CTGGGCA": 3549, "CCTTAAAA": 3550, "GATGGAA": 3551, "TAGATAGATAGATAGA": 3552, "TATGTAA": 3553, "GTACGG": 3554, "TATTCAAA": 3555, "GATCTCC": 3556, "CCTGTTTT": 3557, "TATTGCA": 3558, "GGAAGGAAGGAAGGAA": 3559, "GGTAATT": 3560, "TTACAGA": 3561, "TCAGC": 3562, "GCAAAATG": 3563, "GAGAGCA": 3564, "GTAGAAAA": 3565, "CATTTGAA": 3566, "TCTTCTTTT": 3567, "TCCCATA": 3568, "GTTATTTA": 3569, "CTATCTA": 3570, "CATCCTG": 3571, "TCTTGTG": 3572, "TTATTATT": 3573, "CCCGTC": 3574, "TACTATG": 3575, "TAAACATA": 3576, "TAAGGAAA": 3577, "GCTTGTG": 3578, "CTCTAAAA": 3579, "GTTTTAAAA": 3580, "GACAGGA": 3581, "TCCTAGA": 3582, "TCCACCCA": 3583, "GTTTGAAA": 3584, "CCATCTCA": 3585, "CTAAGAA": 3586, "GTATCTA": 3587, "GTGAGGA": 3588, "GCTGGAGG": 3589, "CCTGTAATCCCAGCTA": 3590, "GCAACAA": 3591, "CTTTCAAA": 3592, "CAAATGTT": 3593, "CTTGTCC": 3594, "TCTCAAAAA": 3595, "TATTTATTA": 3596, "TAAGGCA": 3597, "GAGAGGAA": 3598, "TATGATT": 3599, "GCATCTA": 3600, "CGTTATT": 3601, "GCCTGTA": 3602, "GTTTCAAA": 3603, "CCTTCCTTCCTTCCTT": 3604, "GGCTTTG": 3605, "GTCAGAA": 3606, "CATGCATG": 3607, "GTCATTTA": 3608, "CTGGAAAA": 3609, "CTTCGA": 3610, "CCTATTTT": 3611, "CCAACAA": 3612, "TCCATCC": 3613, "TAAAGTTA": 3614, "GTCTCTC": 3615, "TAATCAAA": 3616, "GATTTTTG": 3617, "GATTTCTT": 3618, "GGGCTGA": 3619, "GCATGTA": 3620, "CCTGGGTT": 3621, "GAGACAA": 3622, "GCTGTCA": 3623, "TGATAGG": 3624, "GGAGACC": 3625, "CCGGCA": 3626, "TAATCTCA": 3627, "TGAATTAA": 3628, "TCTGGTG": 3629, "GCCTC": 3630, "GGCGCA": 3631, "CCAGCTA": 3632, "CAGTCTG": 3633, "TGAACTA": 3634, "GTAAGAA": 3635, "CCTTTCA": 3636, "TCCATGA": 3637, "CAAAGGAA": 3638, "CTCTC": 3639, "CTCTCTCA": 3640, "CTCCAGC": 3641, "GTAGATA": 3642, "CCCCCTCC": 3643, "GGCGCC": 3644, "TCTGTCC": 3645, "GACCATT": 3646, "CTTGAAAA": 3647, "TTATCC": 3648, "TACATGTG": 3649, "CAAATTTG": 3650, "TTTTGTG": 3651, "CAGAGTG": 3652, "GTAATAA": 3653, "GTGAGTG": 3654, "TTTTTCC": 3655, "GGCTCTG": 3656, "GCCCTAA": 3657, "GGCTGTT": 3658, "CCCAATT": 3659, "CAGAGCTT": 3660, "TATAAATG": 3661, "GAGTCTG": 3662, "TCTTAAAAA": 3663, "GTTTTATG": 3664, "GATCCAA": 3665, "GGCCCTG": 3666, "GATCCTG": 3667, "TCAAGTG": 3668, "GATTCAA": 3669, "CCTCTCTT": 3670, "GAGACGG": 3671, "CAGATCA": 3672, "TAAAAGAA": 3673, "CTGAGCAA": 3674, "CCTGCCA": 3675, "CCTTCTA": 3676, "CGCTCA": 3677, "GGCTGTG": 3678, "TGGGAAAA": 3679, "GGAGCCTG": 3680, "CTGAGTG": 3681, "CGTCAAA": 3682, "TCAAGTA": 3683, "CGTAATT": 3684, "TTACTTA": 3685, "TATACTA": 3686, "GGGCAAA": 3687, "CAACTTTT": 3688, "CTTTGCC": 3689, "GCCAGGAA": 3690, "CACACTA": 3691, "GCCCAGC": 3692, "TAAATAAATAAATAAA": 3693, "CTTTCCTT": 3694, "GGGAGAA": 3695, "TATGGTA": 3696, "CGGCCA": 3697, "CCTCTCTG": 3698, "GAAAGCAA": 3699, "CAAGCCA": 3700, "GGCGTT": 3701, "CTCTTTTA": 3702, "TCGGCCTCCCAAA": 3703, "GATTTATT": 3704, "CAAGTCC": 3705, "TATCTTA": 3706, "GTTCAAGACCA": 3707, "CTCACACA": 3708, "GAAATCAA": 3709, "TGAGACC": 3710, "GGGTAAA": 3711, "GCTTGTT": 3712, "GATTTTAA": 3713, "TTTTTATA": 3714, "CAGAGCTG": 3715, "TCTGTTAA": 3716, "GTAATTAA": 3717, "TCTTTGAA": 3718, "CTTGCCA": 3719, "TTTTCATT": 3720, "CCATGTA": 3721, "TCTCGGCTCACTGCAA": 3722, "GGATTCA": 3723, "TCTATTAA": 3724, "TACATAAA": 3725, "GATTGATT": 3726, "GGAGAGGA": 3727, "CGCAAAA": 3728, "GGACTAA": 3729, "TTATGTG": 3730, "GTCACTCA": 3731, "GACAGCA": 3732, "CGAGTT": 3733, "GATGGTT": 3734, "GGAAGAGG": 3735, "GCCAACATGGTGAAA": 3736, "GGAGCCA": 3737, "TGAACTG": 3738, "CCTCTGTG": 3739, "GTATAAAA": 3740, "TCCCAGAA": 3741, "CATTTATG": 3742, "GATTATG": 3743, "TGTTTCTG": 3744, "GAGTGGGTT": 3745, "TACATATT": 3746, "CTCCAGGA": 3747, "GACACTG": 3748, "GGTCTCA": 3749, "CCGGGA": 3750, "TGTTTAAA": 3751, "CTCACCA": 3752, "GGACTTA": 3753, "GCCCACC": 3754, "CAAATCAA": 3755, "GAAATGTG": 3756, "TAGTTAA": 3757, "TCTATAA": 3758, "TTAGATT": 3759, "GTGTAGG": 3760, "TACTGAAA": 3761, "GCACCCA": 3762, "GTGGGCTG": 3763, "GAATGAAA": 3764, "TCTAGTT": 3765, "TCAGGAGA": 3766, "TCCACTA": 3767, "CTCAGTT": 3768, "TACTTAAA": 3769, "GACTCCA": 3770, "TCCATTTG": 3771, "CACAGCAA": 3772, "GCTCATGCCTG": 3773, "GGTGCTG": 3774, "GCTTTCTT": 3775, "GTGGCCA": 3776, "TACGTG": 3777, "GTGCAGTG": 3778, "TGAAGTCA": 3779, "CCTTTAA": 3780, "TCTCAGCTCACTGCAA": 3781, "GAAATATG": 3782, "CCTCAAAA": 3783, "GGGGCGG": 3784, "CGACAA": 3785, "GGTGATG": 3786, "GTCTTAAA": 3787, "CAGAAATG": 3788, "CGTCATT": 3789, "CCAAGCA": 3790, "GGATCAA": 3791, "GTGCTGGGATTA": 3792, "GCTGGCC": 3793, "CGGAGCTT": 3794, "TACATGA": 3795, "TGTTTGAA": 3796, "TCTCCATT": 3797, "TAAGCAAA": 3798, "CCTTTCTT": 3799, "TACTGTT": 3800, "TCCATCTT": 3801, "CTTACTT": 3802, "CGGAGGTT": 3803, "CAAAACAA": 3804, "TCATAGG": 3805, "TTACTAA": 3806, "CTTATTTG": 3807, "GAATGTA": 3808, "CCCCATGGA": 3809, "TTACTGA": 3810, "CGGAAAA": 3811, "CTCCAGTG": 3812, "TGTTCCA": 3813, "CAGATGAA": 3814, "GTTGATA": 3815, "TCCCCCC": 3816, "CATTGCA": 3817, "CTCAGCC": 3818, "CTTACTG": 3819, "TATCCTT": 3820, "CTTTTATG": 3821, "TGAGTAGCTG": 3822, "GACTGAAA": 3823, "CAATGAAA": 3824, "CGACTG": 3825, "CTTGGGA": 3826, "GCAAGCA": 3827, "TCACTCC": 3828, "GATTTGA": 3829, "CATTTTAAA": 3830, "TCAACTA": 3831, "GTCCAAAA": 3832, "CACCCTG": 3833, "TTACCTT": 3834, "CAAGGGG": 3835, "TTTTGGA": 3836, "GTTATTTG": 3837, "GCTACTG": 3838, "CTGAGGCAGGAGAATG": 3839, "GTGATGA": 3840, "GTAGTC": 3841, "TAGTATG": 3842, "GTATAGA": 3843, "GTGTCTA": 3844, "GCTGCTA": 3845, "TTAGTAA": 3846, "TAAACATG": 3847, "GTCACCA": 3848, "CATCTTTT": 3849, "CATATAA": 3850, "TCTCTCTA": 3851, "TTTTATTAA": 3852, "TATTCTAA": 3853, "GAAATTTA": 3854, "CTTCCCTG": 3855, "TAAAGATG": 3856, "TACGTA": 3857, "GTTTATTA": 3858, "GAAAAGAA": 3859, "CCCACCCA": 3860, "CAATTAAAA": 3861, "CCGACA": 3862, "CAAAGTGA": 3863, "CAAACAAAA": 3864, "GCAATTTT": 3865, "CGATTAA": 3866, "TTAGAGA": 3867, "CTGATGA": 3868, "GGAGGAGG": 3869, "GTCCTGGG": 3870, "TCATGAAA": 3871, "GCAACCA": 3872, "GTTGGCA": 3873, "GCGGCGG": 3874, "GTCCCCA": 3875, "GTAGGGG": 3876, "GCCATGTT": 3877, "GTTCGAGA": 3878, "GCCTATA": 3879, "TAAATTCA": 3880, "GGCCATT": 3881, "GAAAACAA": 3882, "TGTGTATG": 3883, "GTACTC": 3884, "TAGGGAA": 3885, "CCTTGAA": 3886, "TCTATTTG": 3887, "GAGGGCA": 3888, "GAAACTGA": 3889, "TACGC": 3890, "TACAAAAA": 3891, "TCATTATT": 3892, "GGAAAATT": 3893, "TCAATATT": 3894, "CCCGTA": 3895, "GGAGAGAA": 3896, "TTAGTTA": 3897, "CTCAGAGA": 3898, "TCGAGC": 3899, "CTAGTCA": 3900, "GATGGCA": 3901, "TGAACATT": 3902, "CTATGGG": 3903, "CACACCA": 3904, "TCAATTAA": 3905, "GGAACTG": 3906, "TTACATG": 3907, "CTTTCATT": 3908, "CAGCTCTG": 3909, "TCTTTTTTTT": 3910, "TAAATCTT": 3911, "TGATCTA": 3912, "CATACAA": 3913, "GCTCAAAA": 3914, "GCTGTGTG": 3915, "TCAATCA": 3916, "GATTTGAA": 3917, "CCAAGGA": 3918, "GTCCTCA": 3919, "GTGCTCC": 3920, "AAAATAA": 3921, "GTGACAA": 3922, "GCTCACGCCTG": 3923, "CGACGG": 3924, "TATCCAA": 3925, "CACACATG": 3926, "TCTCTCTCC": 3927, "TGTGGTT": 3928, "CTTGGTA": 3929, "TCTGGTT": 3930, "TTTATAA": 3931, "CTGCTTTT": 3932, "TGTGTCA": 3933, "CACATCA": 3934, "CCTAATG": 3935, "CGTTTTTT": 3936, "GCTGGCA": 3937, "GACGTC": 3938, "TATAATTA": 3939, "TACAGTAA": 3940, "GAAAGTAA": 3941, "GTCTGAAA": 3942, "CCCATTTT": 3943, "TATATGA": 3944, "CTTGATA": 3945, "CTTTATTTT": 3946, "CTTTATTA": 3947, "GGCGAA": 3948, "CCATGCC": 3949, "CCTGCCTT": 3950, "GAAGAAGAAGAA": 3951, "CTGACTGA": 3952, "GCCCTTA": 3953, "TATCTAA": 3954, "GTGTTTTA": 3955, "TGTGGCA": 3956, "TATTGTAA": 3957, "GCCAGAAA": 3958, "CCCTGTCTC": 3959, "CACAGGAA": 3960, "AAAACAA": 3961, "AAAAAAAAAAAAAAA": 3962, "TAACTCC": 3963, "GCCTAAA": 3964, "CGAGTA": 3965, "TAGTATT": 3966, "GTATTTTTAGTAGAGA": 3967, "GCTGCAGG": 3968, "TATTGAAA": 3969, "CCAGCCTGGG": 3970, "GCTCCAAA": 3971, "TACGAA": 3972, "GGCCTCC": 3973, "TATACAAA": 3974, "CATGGCA": 3975, "CATGCAA": 3976, "TACACCA": 3977, "CTTTACCA": 3978, "TACAGAGA": 3979, "TATTCTTA": 3980, "TATGTCA": 3981, "TCAAGCA": 3982, "TCAATGA": 3983, "GGCTCTT": 3984, "GGAAGTT": 3985, "TCCATGTT": 3986, "GCTTTCC": 3987, "TATGTGA": 3988, "GTGTAGA": 3989, "TTTTTAAAA": 3990, "GCTGGAGA": 3991, "GTGAGAGA": 3992, "CCTAGAA": 3993, "CCTCCAAA": 3994, "CCAATGA": 3995, "CAGGGCA": 3996, "CTATGCA": 3997, "CTTCACC": 3998, "CTACAAAA": 3999, "CTCACC": 4000, "GAGTATG": 4001, "TAGAAAAA": 4002, "CTTTTGAA": 4003, "TAAAGAGA": 4004, "CATGTCA": 4005, "TCTTTTAAA": 4006, "CACAGTGA": 4007, "GATCTAA": 4008, "TAAGGTA": 4009, "CATAGAA": 4010, "CGCGCC": 4011, "CAGCTTA": 4012, "TATAGTT": 4013, "CGGGCC": 4014, "TATCCATT": 4015, "TGTTTGTTTT": 4016, "GCTGGCTG": 4017, "TACAGGA": 4018, "CTCCTTTG": 4019, "CAATCTA": 4020, "CCCCCTG": 4021, "TATACTG": 4022, "CTGAGCC": 4023, "CGGTTA": 4024, "TGAAGTG": 4025, "GCTTCCTT": 4026, "TTTTATTTG": 4027, "TAGTGAA": 4028, "CTGAGGTG": 4029, "TCTTCTC": 4030, "GACAGAAA": 4031, "CTGAACTGAA": 4032, "CCTGGGAA": 4033, "TCCCCAAA": 4034, "TATGTATT": 4035, "GATTTCTG": 4036, "CATTCAAA": 4037, "CACAGTT": 4038, "GCTTGAA": 4039, "GTGGATCA": 4040, "CTGAGTGA": 4041, "TGAATTTA": 4042, "TCAACAAA": 4043, "GGTCATT": 4044, "GTAATTTA": 4045, "GCGACTT": 4046, "CTGAGAGA": 4047, "GTGCCCA": 4048, "CTAGGTT": 4049, "TCCTGAAA": 4050, "GTCCACC": 4051, "TCACAGAA": 4052, "GCGAAAA": 4053, "GTATGGG": 4054, "TGAACAAA": 4055, "TAAACAAAA": 4056, "CCGTTTT": 4057, "TCTCAATT": 4058, "TCCAGAAA": 4059, "GTAACAA": 4060, "GCATTTTA": 4061, "TCTCCATG": 4062, "TTATAAAA": 4063, "CAGGCAA": 4064, "CTAAAAAAA": 4065, "GTTGGGA": 4066, "TAAAGATT": 4067, "TGAAGAGA": 4068, "CCCCTCA": 4069, "TGTTTATG": 4070, "TCTACTG": 4071, "CCAATTTT": 4072, "GGTGGTG": 4073, "GGAACAA": 4074, "TGTGGGA": 4075, "TCTGCTA": 4076, "GAACGA": 4077, "GTAAGTA": 4078, "GTTGCCA": 4079, "AAAATTTT": 4080, "GCGCGA": 4081, "GAAAGATG": 4082, "GTCTCTCA": 4083, "TCCATCAA": 4084, "GCAGCTA": 4085, "CACATTTG": 4086, "CTGACAA": 4087, "TCCACC": 4088, "GCT": 4089, "CCCACTT": 4090, "GCAGGTA": 4091, "GAGGCCA": 4092, "TAAAGTCA": 4093, "CTGGATA": 4094, "CGGCAA": 4095}, "merges": ["A A", "T T", "T G", "C A", "C C", "T A", "G G", "T C", "G A", "AA A", "G C", "T AA", "TT TT", "T CA", "TG A", "TT A", "G AA", "T CC", "C AA", "C TG", "C TT", "G TG", "G TT", "G CA", "GG A", "C CA", "G TA", "G CC", "C TA", "T AAA", "AA AA", "C TC", "G TC", "TG TG", "TA TT", "CA CA", "G AAA", "TA TA", "TC TT", "TG TT", "C AAA", "GA GA", "CA TT", "TG AA", "CA GG", "TC TG", "CA GA", "TC AA", "GG AA", "TAA AA", "C TGA", "GC TT", "G TGA", "GC TG", "C TCA", "CC TT", "CA TG", "GC AA", "G TCA", "G TAA", "TTTT A", "TA TG", "GA GG", "C GG", "GA TT", "CC TG", "TC TC", "CC AA", "G TTA", "C TCC", "C TAA", "TA CA", "C TTA", "TC CA", "GA TG", "TT AA", "GAA AA", "TT TG", "G TTTT", "TC TA", "GC CA", "G TCC", "C TTTT", "GG GG", "C GA", "TT TA", "CC CA", "CAA AA", "TG GG", "TA GA", "TA GG", "GA CA", "GG TT", "CC CC", "GG TG", "CA TA", "GC TA", "TG TA", "TC AAA", "TG GA", "TAA TT", "TTA TT", "TG CA", "GG CA", "GA TA", "CC TA", "TT CA", "TC TCA", "GG GA", "C GC", "CTG AA", "G TAAA", "TC TCC", "TTTT TT", "C GTG", "GC AAA", "TAA AAA", "TC TGA", "TCA TT", "GG AAA", "TG AAA", "TCC TT", "CC AAA", "GAA TT", "C TAAA", "C GTT", "GTG AA", "GG CC", "TAA TA", "GG TA", "TG CC", "CA CC", "TGA TT", "AAAA AA", "GC TCA", "TCC AA", "GA GAA", "CTG TT", "TA TTA", "CA GCA", "CTC TT", "CTT AA", "CA GAA", "GC TGA", "GTT AA", "TC TTA", "TA TTTT", "GCC AA", "CTT TG", "GA CC", "C GCA", "GTA TT", "GTC TT", "CAA TT", "GTG TT", "CTC AA", "GGA GG", "C GAA", "TC TTTT", "GTC AA", "C GCC", "TA TAA", "TA CC", "TC TAA", "CCA TT", "C GGA", "CAA AAA", "CA GTG", "TCC TG", "CTC TG", "GAA AAA", "CTG TG", "CA GC", "TTTT AA", "GCA TT", "GCC TT", "TAA TG", "CTA TT", "GTT TG", "TGA TG", "GG CTG", "CC TCA", "GA GGA", "GCC TG", "AAA TT", "C GTA", "TC AAAA", "TA CAA", "CA TCA", "CA GTT", "TGA GA", "GG GAA", "CA CTG", "CA CAA", "CA GGA", "CC CCA", "CC CTG", "TTTT TTTT", "TA GAA", "GA GCA", "CC TCC", "CA CCA", "TA TCA", "GA GC", "CA TTA", "CACA CACA", "GA GTG", "GGA TT", "TGTG TGTG", "TA CTT", "CA CTT", "GTC TG", "TGA GG", "GA GTT", "GAA TG", "TCA TG", "GA CAA", "GA CTT", "TATT AA", "TAA TAA", "GG CCA", "CA TTTT", "CA GCC", "CC CTT", "GC TAA", "TATA TATA", "GTG TG", "TA CTG", "TA GTT", "CAA TG", "GC TC", "CA GTA", "GC TCC", "CA TAA", "TTA TG", "TAAA TT", "GA TGA", "CA TGA", "GC GG", "AAAA AAAA", "CCA TG", "GA TAA", "GA CTG", "TA TGA", "GCA GG", "GA TCA", "G TTTTA", "GGA TG", "CC TGA", "G TAAAA", "GAA GG", "GA TTA", "CC TC", "GA CCA", "GC TTA", "CC CAA", "AAA TG", "GCA TG", "TA GTA", "TA CCA", "GG CTT", "C GTC", "TC TCTT", "GG TCA", "TTA TTA", "TA CTA", "TA GCA", "TA TC", "CTG GG", "CA TC", "C TTTTA", "C TAAAA", "GTG GG", "GA GTA", "CCA GG", "GA TTTT", "TA GTG", "GAAA TT", "CA CTA", "TC GG", "TCA GG", "CAGG AA", "GC AAAA", "CC TTA", "CA TCC", "CTT GG", "TGTG AA", "TATT TG", "CC TAA", "CTA TG", "GA GAAA", "GAGA GAGA", "GC TTTT", "TA TAAA", "CAA GG", "TC TCTG", "TGTT AA", "TGTG TT", "GA GCC", "GA CTA", "TA TATT", "TAA AAAA", "TTTT TG", "GTA TG", "CATT AA", "TA GGA", "TA GC", "GTT GG", "GAA GAA", "TAAA TG", "TC TGTT", "CA GAAA", "CAAA TT", "TAA TTA", "TC TGTG", "TA TCC", "TGAA TT", "CTC CA", "GTG AAA", "GG CAA", "GGA GA", "GAA GA", "GG TGA", "GG GCA", "CC AAAA", "TCTC TCTC", "CTG CA", "CTT CTT", "TCTT AA", "CC CTA", "TGTG TG", "AAA TA", "TGTT TG", "GG GTT", "GTG CTG", "GG AAAA", "GG GGA", "TCA GA", "CC TTTT", "GAAA TG", "GCA GCA", "TC TGAA", "GG GTG", "CACA TT", "TCTT TG", "GG GC", "TCC CA", "TC CATT", "CTG AAA", "CTT TA", "TC GA", "GTT TA", "CAA CAA", "CTT CC", "GCC TCC", "TT AAA", "GC TCTG", "GTT TCA", "GGA GGA", "C GTGA", "CA GTC", "GAA TA", "CA GAGA", "CC CTC", "CAAA TG", "CTG CTG", "GA TCC", "TTTTA TT", "AAAA TT", "TTA TA", "TCAA TT", "GG TAA", "GTTA TT", "GC CAGG", "GGA GAA", "CATT TG", "TCA CC", "CTC AAA", "GG TTA", "TCC AAA", "TC TATT", "GCA GA", "CTT CA", "TCA TCA", "C GAGG", "TAA CA", "GTT GTT", "CTTA TT", "C GTCA", "TAA GA", "TAA TTTT", "CTG TA", "TC CACA", "GC TGTG", "C GCTG", "TC TAAA", "GC GA", "CAA TA", "CCA CCA", "GAA CA", "C GAAA", "CAGA TT", "TCA CA", "TTA TTTT", "TC TCAA", "TGA CA", "CTCC AA", "AAAA AAA", "TATA TG", "TCC TCC", "TCA CTT", "TC CAGG", "CAA GA", "GG CTA", "GTG GTG", "C GTAA", "C GAGA", "TGA TA", "GGA TTA", "CAA CA", "C GATT", "TGA GAA", "CTCC TT", "CTCA TT", "GTT AAA", "TCA TA", "CC TCTG", "CTC TA", "GC TGAA", "CTG GA", "TAA GG", "CTT AAA", "TATT TA", "CCA CA", "CC GG", "GTC AAA", "TG GAA", "C GGAA", "TGA TGA", "GTT CA", "TAA CAA", "GC TGTT", "TAA GAA", "CTG CC", "TTAA TT", "CCA GA", "TCA GAA", "GTCA TT", "C GCTT", "GATT AA", "CTGA TT", "GC CACA", "GTAA TT", "TC CAGA", "GCC AAA", "GTGA TT", "TAAAA TT", "CAA GAA", "CCA CC", "TAA TCC", "GTT CTT", "TC CATG", "GC TCTT", "TG CTG", "GG GTA", "TTA CA", "GC CATT", "GCA CA", "GCAA TT", "TCC CTG", "TG TGA", "TC GAA", "GGA CA", "GGAA TT", "GTG GA", "CTT CTG", "TCC CC", "GCC CC", "CTT GA", "TAA TGA", "TAAA TA", "TATA TA", "CTG CAA", "TCA TTA", "GTA TA", "TCC CCA", "C GTTA", "GCA GAA", "TGA GTT", "CTTTT TT", "C GATG", "CTT TCA", "AAAA TG", "CAGG TT", "CTAA TT", "C GCCA", "TGAA AAA", "GTT CC", "GTCC TT", "GTCC AA", "GTTTT TT", "CTC TGA", "GC GC", "GTT GA", "TGAA TG", "CTA TA", "GCA GTG", "CCTT AA", "TCA CCA", "TCA CTG", "GCC CTG", "TAA CTT", "CAGA TG", "GTA GG", "TC TATA", "GAGA TT", "GTC TA", "TTTT AAA", "CACA TG", "TGA CC", "CA CAAA", "GTG TA", "GG GAGG", "GCTT TG", "CAA AAAA", "GA GGAA", "GTT CTG", "TTTT TA", "GTC TCA", "GTT CAA", "TC GTG", "GCTT AA", "GCA CC", "CTCC TG", "TAAA TAAA", "CTA CA", "CTT CCA", "TCC TCA", "C GCAA", "GAA AAAA", "GCC CA", "TC GTT", "GTA GA", "CTC TCA", "GTC CA", "TGA CTT", "TCC CTT", "GC CATG", "CACACACA CACACACA", "GTGA TG", "CC TCTT", "GC CAGA", "TCC TA", "C GTTTT", "GTA CA", "GCA TA", "GAA TTA", "TGTGTGTG TGTGTGTG", "CC CAGG", "GG TTTT", "TCAA AAA", "TC TATG", "CCA TA", "TGA CAA", "GGA TA", "TCA GTG", "GTA TTTT", "GAGA TG", "GC GTG", "C GTCC", "TTAA AAA", "TAA TCA", "CAA TTA", "CCA CTG", "CGG TT", "GTT GAA", "TGA TTA", "CCTT TG", "CGG TG", "CAGG TG", "TCAA TG", "CTGA TG", "TCA GGA", "GTT TAA", "TATT AAA", "CTC TTA", "GCA GGA", "CTC TCC", "GAA CC", "CTT TAA", "GG GCC", "GTA TTA", "GC GCC", "CCAA TT", "GC TAAA", "TGA CTG", "GATT TG", "GA TAAA", "TCA GCA", "GTT CCA", "GAAA TA", "GA CAAA", "GA GTC", "GC TATT", "TCA CAA", "GAGG TT", "TAA CC", "GAA GGA", "GC TCAA", "GAAAA TT", "CCA GCA", "GTTTT AA", "GTG CC", "TGA GGA", "CA TAAA", "GG TCC", "TCA TTTT", "TATT TATT", "TAA TAAA", "GCC TA", "CTTTT AA", "TAA GTG", "TAA GTA", "CTG GAA", "CACA CA", "GA CAGA", "CAA CC", "GG GAAA", "CCA GAA", "TCA GTT", "TAA CTA", "CTAA AAA", "TGGG TT", "TGA GTG", "TAAAA TG", "TATATATA TATATATA", "GCA CTG", "GA CTC", "TA CAAA", "TAAAA AAA", "TC TACA", "GTT GTG", "TC GCC", "CC CAAA", "GTCA TG", "CTG CTT", "GGAA TG", "CTA TTA", "GA TATT", "TA GAAA", "GG CAGG", "GA TGAA", "GTA GAA", "TCC TGA", "TAA CTG", "GCTG GG", "GCAA TG", "GCC CCA", "GTT TGA", "CATT TA", "GTG CA", "CTT GAA", "GTG GAA", "CTT CAA", "TAAA TTA", "GTG GCA", "TCC TTA", "GGAA AAA", "TTTT TTA", "CC TGTG", "GTAA TG", "GTG TTA", "CTA GG", "CAGG CTG", "GA CACA", "GAAAA AAA", "TC GC", "GTAA AAA", "TGTT TA", "TCTC TA", "GTCC TG", "CCA GGA", "GAA CAA", "TAA GTT", "TGA GCA", "GC TCCA", "TAA GCA", "CTCA TG", "GTC TTA", "CC CACA", "CA TATT", "GCC TCA", "CA CTC", "CTT CTA", "TGA TTTT", "TC GCA", "CC TGTT", "GAA GCA", "GCAA AAA", "GC GGA", "CCA CAA", "GC GCA", "CA TATA", "GA CATT", "GTT CTA", "CAAAA TT", "GAAA GAAA", "CC CGG", "TA CACA", "CCAA AAA", "GAGG TG", "GG CTCA", "CA GTGA", "TCC CAA", "TA TCTT", "TGA GTA", "TC GTA", "TTTT CTT", "GTG GGA", "GA GCTG", "CC CTCC", "TAGG TT", "TTA GG", "TAA TATT", "CCA GCC", "CA TCTT", "GTC TGA", "GTT TCC", "CC TGAA", "GGA GCA", "GAAAA TG", "TCA GTA", "TAA CCA", "GA TGTT", "CTG TTA", "CA TGTT", "GG CGG", "CA TGTG", "GG GAGA", "CTT TGA", "TCTT TCTT", "AAAAAA AAA", "GGGG TG", "CTT TCC", "CTT GTT", "GCA TTA", "CC CAGA", "CAAA TA", "TC GGA", "CA GCTT", "TCA CTA", "TAA TTAA", "TAA GGA", "GAA CTG", "GCA CAA", "GC GTT", "GG CTC", "TC TTTTA", "CC TCCA", "GG CAAA", "CA GCTG", "CTA CAA", "TA CATT", "GC TATG", "CTT GTG", "GA GTCA", "GTTA TG", "CTG CCA", "GTC TCC", "TGA CCA", "CA CCTG", "TATA TTA", "TGA TCA", "CA GCAA", "GA TGTG", "GTC TTTT", "CTA GAA", "GC TACA", "CTG GGA", "GGGG TT", "CAA GTA", "CAA GGA", "CC CTCA", "TA GCC", "GTT GGA", "GC TATA", "TCTG AAA", "TA TGTT", "CC CCTT", "GTT GTA", "CC CTGA", "TGA CTA", "CAA GCA", "CAA TAA", "GAA CTT", "CA TGAA", "CTTA TG", "CTAA TG", "TC TAAAA", "CCAA TG", "GAA GTG", "CC TCAA", "CC CATT", "CA GTCA", "GAGAGAGA GAGAGAGA", "TA TGTG", "GCA GTGA", "TCTCC TT", "TCC CAAA", "CCA TTA", "CCA GTG", "GCA TCA", "TCAAA TT", "GA TCTT", "GA CAGG", "GGA GTG", "GTA GTA", "CAA CTT", "GAA GTT", "CC CCTG", "TCTC AAA", "GG GTC", "GA GCTT", "TATG AAA", "TA TGAA", "GA CATG", "CAA GTG", "GA TATA", "CA TCTG", "CTG TGA", "TAA TTTA", "GG CAGA", "GC GAA", "CC TAAA", "CCA TCA", "CA CTGA", "GGA CTA", "GA CGG", "CTC TTTT", "CTG TCA", "TCTCTCTC TCTCTCTC", "TTAA TG", "GCA GCC", "CAAAA AAA", "GCA CCA", "CTA TTTT", "GA GCAA", "CTT GGA", "CTG GTG", "GAA TAA", "TCC TTTT", "GAA GTA", "CA GTAA", "CAA CCA", "CTG TAA", "TGA TAA", "GCA GTT", "CA CGG", "TAAA TAA", "CTG TTTT", "CTA CTA", "GC TCTA", "C GAAAA", "CAA GTT", "CTT GTA", "GAA TGA", "GA GTGA", "GCC TGA", "GG TTTG", "CC CATG", "GG GGAA", "GAA GAAA", "TG TTA", "CAA TTTT", "TATA TTTT", "CTC AAAA", "GG TGGG", "CC GTG", "TATT TCA", "CC CCAA", "TATT TAA", "GG CTGA", "GG TGTG", "CA TCAA", "CA CTCA", "TCTCA TT", "GAA TTTT", "GAA TCA", "CAGG AAA", "CA TACA", "TA TTTTA", "TTA TAA", "GAGG AAA", "CA TATG", "CTT TCTT", "CAA CTG", "GG GCTG", "CC CCCA", "TTTG AAA", "CATT AAA", "CTT AAAA", "GA CTGA", "CAA TGA", "GG CACA", "CCA GTA", "GGA TGA", "GTTTT TG", "GCA TTTT", "GTG CCA", "GCA GTA", "GCC CTT", "TC GTC", "GAA CTA", "GTG GTT", "GTG TGA", "GTG CTT", "C GCTA", "GTG TCA", "TCTT TA", "GCC TTA", "CC TATT", "CAAAA TG", "GAA CCA", "CTC CAGG", "GA CTCA", "CATG AAA", "GC TAGG", "TGTT AAA", "GC GTA", "GCA CTT", "TCTT AAA", "TAA GAAA", "GG CCTG", "TCC CTA", "GTG GTA", "CTG CTA", "GGA GTT", "GG TAAA", "CAAA CAAA", "GA TATG", "TCA TGA", "GA CCTT", "TAA TATA", "GC TAGA", "GGA CTG", "GG CATT", "CA GTTA", "CC CTAA", "CA CCTT", "GG TGAA", "CA GCTA", "GTG TTTT", "CAA CTA", "GA TCAA", "GA GAAAA", "TGTG AAA", "AAAA TA", "GATG AAA", "CTC TAA", "TTA CTT", "GA TCTG", "CCA CTT", "GA GTTA", "CAA TCA", "GGATTA CAGG", "TTTA TTTT", "TACA TA", "TTTTA TG", "GA GTAA", "GCTG AAA", "GTA CTG", "GC TCTC", "TATG TA", "TGTG TA", "TCA TAA", "GGA CTT", "TCTCC AA", "GCA TGA", "GA CGA", "CGCC TG", "GA CCTG", "GG TCTT", "CA CCAA", "GA TC", "GA CCAA", "AAAA TTA", "GTAAA TT", "CCA GTT", "CA GAAAA", "TAA CAAA", "GG TGTT", "GAAA TTA", "TGCC TCA", "CC GCC", "CCA TTTT", "CTT GCC", "TCTG TA", "CTG GCA", "GG GATG", "CCA TGA", "CTA CTT", "TAGG TG", "TAAAAA TT", "GAAA GAA", "TAAAA TA", "CTTTT TG", "GTC AAAA", "GGA CAA", "TCTGA TT", "CTC TCTT", "TAA TTTG", "CTC TTTG", "GG CCTT", "GGA TTTT", "CTA CTG", "GTT GCA", "GG CTCC", "CTC TGTG", "CTC CAGCC", "TTA CAA", "GGA CCA", "GGAA GGAA", "TAAA GAA", "TTA GAA", "GTG AAAA", "CTT GCA", "TGGG TG", "GGA GCC", "CC TCTA", "C T", "GG GCTT", "GG CATG", "CTG GTT", "TA CAGA", "GATT AAA", "CTC TGTT", "TTA TCA", "CTG AAAA", "GTA GTT", "GG GTCA", "G T", "CA GCCA", "GC GTC", "CA CTTA", "GTG CTA", "TC TTATT", "GTA CTT", "GG TATT", "TA GAGA", "TA CATG", "CCA CTA", "TGA GAAA", "CAA TAAA", "TCC AAAA", "CGTG AA", "GG TCTG", "CTGAA TT", "TCA GCC", "CC TCTC", "GTT AAAA", "GG GATT", "TCC TAA", "CA CTAA", "GGA GAAA", "CCTT CCTT", "GTT TCTT", "TA TCAA", "GA TACA", "TAATCC CAGCA", "CC GCA", "TGAAA TT", "C GTAAA", "CTC TCTG", "TC TTTTTT", "GTA CAA", "CCAAA TT", "TGTA TTTT", "TC GCTT", "GG GTGA", "GA TAGA", "CTT TATT", "TAAA CAA", "GTT TATT", "TGAA TA", "CTA CCA", "GTG TCC", "CC CGA", "TTTA TTA", "CTCC AAA", "TTTTTTTT TTTT", "TCA TCC", "GAA GCC", "CTAAA TT", "CAAA TTA", "CCCC AAA", "TCTT CTT", "TAGG AAA", "CA CGA", "CA TTTTA", "GTG CAA", "TCTCC TG", "TATTTT AA", "GTT TGTT", "GA GCCA", "GG CCAA", "CATT TCA", "CA TCCA", "CC TATA", "GA CTTA", "TCAAA TG", "GTA TCA", "TAAA TTTT", "CTGA GGCA", "GCC CAA", "GG TTAA", "TA TCTG", "TGA CAGA", "GGA GAGA", "GCTG CTG", "CC CTTA", "TCC TCTG", "GTA GCA", "CCTG AAA", "CC GAA", "TTTT TAA", "CTA TAA", "CCTG TA", "TTA CTG", "GTA TAA", "GG CGA", "GA CTAA", "TCA GAAA", "GTG TGTG", "CAAA GAA", "CC TATG", "GCA GAGA", "CC GTT", "TTTTA TTTT", "GGAA GAA", "TTA CTA", "GCC TGGG", "TCC CTC", "TCC TCTT", "GGA TCA", "GG TCAA", "TC GAGA", "TATT CTT", "TA CTC", "GTTAA TT", "GC GAGA", "CTTAA TT", "TCC TTTG", "GTC TAA", "CA CCCA", "GG GTTA", "GG GCAA", "GGAAA TG", "GCAAA TT", "TA GATG", "GCA GAAA", "AAAAAAAA AAAAAAAA", "CC TACA", "GGA GTA", "TC TAATT", "CAA CAAA", "TA GATT", "GG TTTA", "CC TAGA", "CTT TAAA", "TA CTTA", "TAA TGAA", "CTA TCA", "TA GTAA", "CAGA GAA", "CAA GAAA", "GGGG AAA", "CGTT AA", "CGTG TT", "TCTG TCTG", "TTTTAA TT", "CTG GCC", "TAAA TGA", "C GTCAA", "TTA GTA", "GTC TCTG", "TTTT AAAA", "CA GTTTT", "CTT CCTT", "TATA TAA", "GC TTTTA", "TTTT TCA", "GG TC", "TTA TTAA", "TTTT GTT", "CA TAGA", "TA GGAA", "GAGA GAA", "GTA GCTG", "TTA TGA", "GTA GTG", "GGA GAGG", "CTC TGAA", "TA GTC", "GA CTCC", "TCC CTCC", "TAA TGTT", "CA TCTA", "GCCA CCA", "GTA CTA", "TGGG AAA", "CGCC TT", "GCC CGG", "GGA GGAA", "GTA CCA", "CGC AAA", "CA TAAAA", "TAA CATT", "GC TAAAA", "TCTT CTG", "GCC AAAA", "GTA TGA", "GTC TTTG", "TA CTGA", "TCC CAGG", "TTA TTTA", "TTA GTT", "GGA CC", "TA TAAAA", "CAAA CAA", "CTT CTC", "TCTA TCTA", "GAAA TAA", "GTG TAA", "CTT TGTT", "GA TAAAA", "GCC CAGG", "GC GATT", "AAAAAA TT", "TA CAGG", "GG CTAA", "TA GCTT", "GTC TCTA", "CTCC TGA", "GAA TAAA", "TTA CCA", "GG GACA", "GCCA CTG", "GTT TAAA", "GTC TGTG", "TGA CAAA", "TACA TTTT", "GCCA CC", "TG TTTT", "TA GCAA", "TTA TAAA", "GA CCCA", "GCA GC", "CAGA CAGA", "CA CAAAA", "GCC CTA", "TATT AAAA", "C GTATT", "CCA TCC", "TC GATT", "GAA GGAA", "GA TCCA", "TATT TGA", "GTGAA TT", "TA CCTT", "C GTCTT", "CC TAGG", "TC GAAA", "CTT TCTG", "TGAA GAA", "TCTC TCA", "GTC TCTT", "GGA GGGG", "GTC TGTT", "CTA TGA", "GGAAA TT", "GCA CACA", "GCC TTTT", "CA GTCC", "CTG GTA", "GCA TCC", "TA GTTA", "GG CTTA", "GA GTCC", "TG AAAA", "TAGA TAGA", "TGTT TGTT", "TA CTCA", "CATT TAA", "GA TTTTA", "CA CTCC", "GAAA CAA", "GC GCTG", "TCTT TCA", "CTG TCC", "GAA CTCA", "CGG AAA", "TATT GTT", "GCA CTA", "TATT CAA", "GC GGGG", "GTG GCC", "TAATT AAA", "TA CTAA", "GC GGTG", "TA CCAA", "GG TATA", "CTA GTT", "GCA GAGG", "CTTTT TTTT", "TTTTTTTT TTTTTTTT", "TACA GTA", "CCA TGTT", "TA GTGA", "CGTG TG", "GC TCTGA", "CTT CCTG", "TC GCTG", "TAAA TCA", "TCCAA TT", "GTT TCTG", "GAA GAGA", "GG GTAA", "CCA TAA", "TTA TATT", "C GAATT", "CC GGA", "TGA GCC", "CC GTA", "CAGA GGA", "GTG TTTG", "GA CAAAA", "TTTTTT AAA", "GTT GCC", "GA GTTTT", "TC AAAAAA", "TGTT TCA", "TA TCTA", "TCTC TCC", "CTC CACA", "TAAA TATT", "TTTT CTG", "CTC TCAA", "CCTT AAA", "TCTTTT AA", "GAA CAAA", "TTA GCA", "GCTCA TG", "TAAA GTA", "GGA TAA", "TTATT AAA", "CTC CATT", "TCTC TGA", "TTA TTTG", "CCTG TAA", "TTA TATA", "GA CTTTT", "TGTT GTT", "GCAAA TG", "CTT CAAA", "GAA TATT", "GAA TCC", "CTC TTAA", "GCA TAA", "GAA TGAA", "CTTAA AAA", "TAAAAA TG", "TTTTAA AAA", "CTC TGGG", "TGA TCC", "GC TCTCA", "CTC CAGA", "GAGTG CAGTG", "CAA TATT", "TA GAAAA", "GTAAA TG", "TA GCTG", "GC TCAAA", "GCA GGAA", "TA CCTG", "GG GAAAA", "TTTT CTA", "GGGG GGGG", "CC GA", "CTT TGAA", "GGA GGTG", "TA GTCA", "GG CCCA", "TGA TGTT", "CAAA TAA", "TCTT CCA", "GC GCTT", "GTA TTTG", "GTC TC", "GAAA TCA", "TGA TAAA", "CATT CTT", "TA TCCA", "GCC TCTG", "TGA GATG", "C GCCAA", "GTTTTA TT", "TATA TATT", "GTA GGA", "GACA GAA", "CTCCAGCC TGGG", "GC GTGA", "GG TATG", "GAGG GAGG", "TCA TTTG", "CTA CC", "TACA GAA", "GG TAGA", "GA TCTA", "GTC CATG", "TGA GGAA", "TAA TAAAA", "TAAA CTT", "TCA CATT", "GGA GGCC", "TCA CAAA", "CA CTTTT", "CGG CC", "CAA CAGA", "GTA GAGA", "GTTA TTTT", "CGTT TG", "TC GTCA", "TCTG CTG", "CAA CACA", "GG TAGG", "GCA GCTG", "TAGTA GAGA", "CAA GCC", "GCA TTTG", "TAA TATG", "GCTT AAA", "GCTT CTG", "CTC TCCA", "TCA TCTT", "C GTCTG", "TCA TTTA", "CA TAGG", "GC TCCTT", "TGTT CTT", "TACA TTA", "CACA GAA", "TAAA TATA", "TA GAGG", "GA TAGG", "TCC TGAA", "GGA GCTG", "TGA TATT", "TCA TTAA", "CTTTT AAA", "TC GTTA", "TAAA CTA", "GTT TGAA", "TAAAA TTA", "CA CCCC", "TCA GAGA", "CTCC TGCCTCA", "TGA CATT", "GTA TTTA", "CTT CATT", "GAAA CTG", "TAA CACA", "GTT CAAA", "GGA GATG", "TC GGCC", "CAGCA TT", "TC GATG", "TATT CTA", "CTG TGAA", "TATT GAA", "TTTT CCA", "TATT TCTT", "GGTG AAA", "CTGA GAA", "GCA CAGA", "GC GAGG", "CTG TGTG", "TGAAA TG", "TGA TGAA", "GTCC AAA", "CTCAA TT", "TCCA GAA", "GTA TATA", "TAAA GTT", "TCTC AAAA", "TCCA TCA", "GTC TGAA", "TGA GAGA", "TGA TTTG", "TTA GCC", "CTC CATG", "TCC CTGA", "GA GCTA", "CCCC CCCC", "GTG GAAA", "CTG GGAA", "CAA TGAA", "CCA CACA", "CTT TCAA", "C GGAGG", "TC GTGA", "CCA GAAA", "GTTTT AAA", "TGTT GAA", "TCC TGTG", "CTAAA TG", "TCC TTTA", "GTC TGGG", "TCTC TTTT", "TA CGG", "TATT GTA", "TTA GTG", "TTA CC", "TAATCCCAGCA CTTTG", "TCTG GAA", "CTT CTCA", "CGCA TT", "TATT TAAA", "TCA CACA", "TAA TCAA", "GC GAAA", "GG GCCA", "GTT CATT", "GAGAA AAA", "TTTT GTA", "TA CTTTT", "TC GAGG", "GTGAA AAA", "CAA TATA", "TCC CATG", "CAA TTAA", "CTG GAAA", "CCCA GCA", "TCC CATT", "TCC TGTT", "CTC TTTA", "TCC CCTT", "GTT TCAA", "GTC CAGG", "GGAA GGA", "TA GTTTT", "TGA CCTT", "GTGCTG GGATTACAGG", "TATT TATA", "TCTG CAA", "CTGAA AAA", "TATG TTA", "CTT CACA", "GCA CAGG", "CCTG CTG", "TTTT TTAA", "GTTA TTA", "CC CTTTT", "TGA TTTA", "TA CAAAA", "TAA GTAA", "TTTT TAAA", "CA TCTC", "GTG GTGA", "GTG GAGA", "CTC TGCA", "GTTAA AAA", "TACA TACA", "CTT TGTG", "GGA CACA", "TCTGA TG", "TA TTATT", "TCTT CTA", "CTG TGTT", "TCA GCTT", "CTT TATA", "GG CGC", "TCC CTCA", "GTA CC", "TGGA GAA", "CAAAAA TT", "TCTT TAA", "CTC TCTC", "TGA GTGA", "GCA GCTT", "CGGA TT", "TA CGA", "TCTT GTT", "TC GTAA", "GCC TGTG", "TATT CTG", "GG GATA", "GG GTCC", "TGA GATT", "CTTTTA TT", "TCC CACA", "CATG GTG", "TTA GGA", "GAA CACA", "TCA TAAA", "CAA CATT", "GG TCCA", "GAA TTTG", "TATTAA TT", "TCC TGGG", "GCA GCAA", "CTC TTCA", "GAA GAGG", "TCTG TCA", "CTGAA TG", "CCA CAAA", "GTG GAGG", "TGA TTAA", "CTCC CTCC", "CACACACACACACACA CACACACACACACACA", "GC GATG", "CATT CTG", "GTA GAAA", "TCA TCAA", "TTTT CAA", "TATG TATG", "CCAAA TG", "TAA TTTTA", "TAA GGAA", "CTT GAAA", "AAAAAAAA AAAA", "GC TCCTG", "GCA GATG", "GAAAAA TT", "GA CGC", "GTG GGGG", "GTCAA TT", "CTT GCTT", "TGA CACA", "GTG TGTT", "CCA GAGA", "CCCA GCC", "TAAA GAAA", "GTC CATT", "TAAA TTAA", "CC CAAAA", "GAA TTAA", "TGAA TTA", "TTTT TTTG", "CCA GCTT", "CAA TTTG", "CTG TTTG", "GTC TCAA", "GTT TGTG", "GG CATA", "GG TACA", "TGA TGTG", "GATT TCA", "TCTG CTT", "GTAA TTA", "TAA AAAAAA", "GCC GCC", "TGTGTGTGTGTGTGTG TGTGTGTGTGTGTGTG", "GC GTCA", "GC TCATT", "GAA CCTG", "TAAA CAAA", "GTG CTGA", "TCA GGAA", "TCC TCAA", "TCTA TTTT", "TCTG TTTT", "CAGA GCA", "CCA GGAA", "GTC TTTA", "TCTT CAA", "TCAAAA TT", "GC TTATT", "GTT CCTT", "CA CCTA", "TCA CTGA", "GAA GCAA", "TAAA GA", "TCC TTCA", "TCTCA TG", "TCA GTGA", "TACA CAA", "CA CGTG", "CC TAAAA", "GCC TTTG", "GG CTTTT", "GTT GAAA", "GTT CTC", "CTA GA", "CTA CAAA", "GCA CAAA", "TTA CATT", "GG CCCC", "TAA TGTG", "CTG CCTT", "TCC CAGA", "GTGAA TG", "GGA CAGG", "GGA TGTG", "GTT TATA", "TGA CCAA", "GTG GCTG", "GTT CTCA", "CTTA TTTT", "CTG GAGA", "TTA CAAA", "GTC TTCA", "CAA GAGA", "CCA TTTG", "TCA CAGA", "CTA GTA", "CA TTATT", "TTA GA", "GC TCTCC", "GC GCCA", "TATG TTTT", "TCC TCCA", "CAGAA AAA", "GTG GGAA", "TAA TCTT", "TGA GTCA", "CTG CTC", "GTC TCCA", "TCA TGTT", "GTT TCCA", "TAA GCAA", "CTAA AAATA", "TGA CTGA", "TC GGTT", "TTA GAAA", "TAA GCC", "TAAA GCA", "CC TCTCC", "CC TCCTT", "TCA GATT", "TATG AAAA", "GCTGA TG", "CATA TTTT", "GC TCCAA", "CGG CGG", "CCA CTGA", "CA GCAAA", "CTG TCTT", "CTA GCA", "TC GGGG", "CACA GCA", "GC TGATT", "CTA GGA", "TAA CTC", "TCA TATT", "CCTT CTT", "CTG CAAA", "CC CGC", "GG TCTA", "CCCA GGA", "GTG TCTG", "TAATAA TAATAA", "TCA CATG", "CAA TTTA", "TATATATATATATATA TATATATATATATATA", "CCA CAGA", "TCAA TTTT", "GTA TTAA", "GAA CATT", "TCTC TTA", "CTA TTTG", "TCTT TCC", "GGTT AAA", "GC TAATT", "CTG CTGA", "TA CCTA", "CAGG GTT", "TC GCCA", "CAAAAA TTA", "CTT CTGA", "GCA TGTG", "CTA TTAA", "GCA CATG", "CAA CATG", "TCA TGAA", "GAA TGTT", "GG GTTTT", "CTG CCTG", "GTC CACA", "TAAA CA", "CTC TGGA", "GA CCCC", "GG CAAAA", "TCTG TTA", "CTA GTG", "CTA TATA", "TCA GTCA", "TAA CTAA", "GAA GATG", "GTC TTAA", "CAA GGAA", "GTAA AAAA", "TCC CCTG", "TC GCAA", "TCTG CCTG", "CC TTTTA", "GTCC CAGCTA", "TATA TATG", "TATT GTG", "TGTG TTTT", "GC GCAA", "CACA GTG", "TAA GATT", "CTC TGTA", "GGAGG CTGA", "GGA CAAA", "TATTAA AAA", "TC GTCC", "TC GGAA", "CTA TAAA", "CTT CAGA", "CTA GAAA", "CATT CAA", "CA CGCA", "CAGGA TT", "CCA TCTT", "GTA GCC", "GAA TTTA", "CA CGC", "CAA TCC", "TGA GCAA", "GAA GCTG", "TCAA TTA", "GAA GTCA", "CTG CACA", "CCA CGG", "GGA TCTT", "CTCCTGCCTCA GCCTCC", "TAAA TGAA", "CC GTC", "TC GGTG", "TTTTA TTA", "GCA GGGG", "GCA GGTG", "TCTA TTA", "TAA CTTA", "CTAA TTTT", "CC CGCC", "TAA TACA", "GGATT AAA", "TCTC TCTG", "GCTT CTT", "CATT TATT", "CCA GAGG", "GGA CAGA", "GCCAA TT", "TCC CCAA", "GTT GATT", "GAA GAAAA", "GCA TTTA", "CTC TAAA", "CACACACA CACA", "CC TCAAA", "TA TAATT", "CAA TGTT", "GCC CAGA", "GTA TATT", "CTAA AAAA", "CCA CAGG", "TAA GAGA", "TCC TTAA", "TA TTTTTT", "GAA TATA", "GGA TTTG", "GTG TGAA", "CTG GCTT", "GC GGCA", "TCC GCC", "GCA TCTT", "TC TAATA", "CTG CATT", "CTC TGCC", "TCA CTCA", "TCA GCAA", "TATTA TG", "CCA GCTG", "GA TCTC", "GCC TCTT", "CTT CCAA", "TCC TAAA", "TCA TCTG", "CTA TTTA", "CTG CAGG", "CAA GCAA", "GC GGAA", "GAAA TAAA", "TAAAA TAA", "TCA CCTT", "CCA TGTG", "GA CCTA", "CAGA TGA", "GTG GCTT", "TTATTA TTATTA", "TCC CGG", "TATT TGTT", "CTG TAAA", "TCCA TCCA", "CTG TATA", "GTT TCTA", "GTT GCTT", "CCA TGAA", "GC TCTTA", "CTT CATG", "GTT CCTG", "GCTG GGA", "TCA GAGG", "CATT AAAA", "TCA GTAA", "GAA TGTG", "CTTA TTA", "GCA CTGA", "TGA GGTT", "CA TCAAA", "CTT CTCC", "GTT TATG", "CTT TCCA", "GTG CCTG", "GAAA GGA", "GCA TCTG", "TA CCCA", "TAA CAGA", "AAAAAAAA AAA", "CTA TGAA", "CA GTAAA", "TA GCTA", "TC GTTTT", "GTG TCTT", "GA GCAAA", "TC TAAAAA", "GTT CACA", "GAAA TGA", "CAAA TGA", "GCC CTGA", "GTG TTTA", "TCA TGTG", "CATA TTA", "TCAAAA AAA", "TAA GTTA", "TCTC TCTT", "CCA GTGA", "CC TCTGA", "CAA GATG", "GCC TGTT", "GTT TGGG", "CATT CATT", "GCC CCTG", "GTT CTGA", "GC GGCC", "GC GGTT", "CAAAA CAAAA", "TACA TATA", "GAATT AAA", "TCAA GAA", "CTG TATT", "TTTT TATT", "GA TTATT", "TCTAA TG", "GTT GCTG", "TGAA TGAA", "TCA GCTG", "CTT GATT", "CAGAA TG", "CTAA TTA", "TATAA TG", "GTTTT GTTTT", "CCA GCCTG", "TGA TGGA", "GCA GATT", "CTC TATT", "GCA GTCA", "TAA GTGA", "CTA CACA", "CGCA TG", "TA GCCA", "GTG GCTCA", "CAAA TAAA", "GTG CTCA", "TTTT TTTTTT", "TAA CATG", "TCCCA GCTA", "CAAA GTA", "TCA TATA", "CAGCA TG", "TGA TCTT", "CA TAATT", "TGTG TTA", "TTTT GAA", "TTAA TTA", "GATA TTA", "TCA TTCA", "TGA TATA", "TGA CTCA", "GA CGTT", "TGA CATG", "GTT GTGA", "CA TTTTTT", "GCC TGGA", "CTA TGTT", "CTT TGGG", "GTC TCAAA", "CTG GCTG", "CCA CATG", "GG CGTG", "CTTAA TG", "TAA GATG", "GTA TAAA", "TGTA TTA", "TAA CTCA", "GAGAGAGAGAGAGAGA GAGAGAGAGAGAGAGA", "GCA TGAA", "GTTAA TG", "TCCA GGA", "GAGA GAAA", "TCTC TGTG", "CTC TCTA", "CCA CCTG", "GCCA GGA", "CTG GAGG", "CCA TTTA", "GTC TGGA", "GCC CACA", "TAGA GAA", "CAA CTCA", "GGCA GGA", "TCTTA TG", "CAAA GGA", "GG TAAAA", "GAGA GGA", "GTC CAGA", "GCC CTCA", "GATA TTTT", "CAGG GAA", "CCA CATT", "GA GGAGG", "GAAA CTT", "CA GAATT", "TCA GATG", "TATT TCC", "TACA GTG", "TGA GCTG", "CCA TCTG", "GAGAA TG", "TCAA CAA", "A TT", "TAA CTGA", "TGA GAGG", "CA CTGAA", "CCA CCTT", "CTG CAGA", "TCA CCAA", "TGA GCTT", "CAAA GCA", "GG TTTTA", "CGG GGTT", "TCCAA AAA", "TATG TATA", "CCA GATG", "TCCA TTTT", "CTG CTCA", "GA TAATT", "CCA CCAA", "CTCC TCC", "GA GAATT", "GAAA GTA", "TAAAA TAAAA", "CTT CTTA", "CTG TTTA", "GAA TCAA", "GCA TGTT", "GCA CGG", "GA CTGAA", "GTG CACA", "GA CGTG", "TATA CAA", "TC GACA", "GAA GACA", "TAAA GGA", "GA TCAAA", "CAGTG TG", "CTA GCC", "GAGG AAAA", "TCTG AAAA", "GAA CCCA", "GATG GATG", "GTT CTTA", "CTA TATT", "GCA TTAA", "TCTCTCTCTCTCTCTC TCTCTCTCTCTCTCTC", "TCA GTC", "TATTTT TG", "GAGGA TT", "GTA TGTG", "TAA CCAA", "GTT GTTTT", "TTTT TCTT", "GTG TTAA", "CTT GGAA", "AAAAAA TG", "CAA TGTG", "GTG CCTT", "GCC TCAA", "GA GTCTT", "GCTAA TTTT", "CGAA AAA", "GTG TATA", "GC GTTA", "CTGCA CTCCAGCCTGGG", "GTT CATG", "CAAA GAAA", "GCA GTAA", "GGA TGAA", "CTT TATG", "CAGG AAAA", "TCC TGCA", "CTG TCTG", "GAA CATG", "GGA TGGA", "GCC TGAA", "CAAAAA TG", "TCCAA TG", "CCA GCAA", "GG CCTA", "CAA CTGA", "GCA CCTG", "GTC TATT", "CC TCTCA", "GTG GTCA", "GTG TAAA", "GTA CACA", "GTAAAA TT", "GTA CATT", "TATA TAAA", "CTG TTAA", "TAA GTCA", "GCC TCCA", "AAATT AAA", "GTG CAGG", "TCC TGGA", "GTG CAAA", "GC GTCC", "CCA TTAA", "GGA GGGA", "TCA CTTA", "TCATT AAA", "CAA CATA", "TAA TAGA", "TAA TGTA", "GA TTTTTT", "GTT GTCA", "GGA GACA", "GTG TGGG", "TCA CAGG", "TC GGCA", "CTCC CTG", "GA CCAAA", "TGTT TATT", "CGAA TG", "CTCAA TG", "TCA CCTG", "CA GTGTT", "TGA GACA", "TA GGGG", "GAAAAA TG", "GTT GAGA", "TC GATA", "CTC GGGAGG", "GTT GTC", "CCA GTCA", "GCC CAGGCTG", "GAA CAGA", "GGCTCA CTGCAA", "GCA GACA", "TGA GGTG", "CA CGTT", "TAA GAAAA", "CCA GGCA", "GTA TCTT", "CTTGG GAGG", "CTT TCTA", "CC GCTG", "GA GCTCA", "GAGA CAGA", "CTT CAGG", "GCA CATT", "GTA CAAA", "CTT GTAA", "GTG GGTG", "GAA GTGA", "GG TCTC", "GTA TGTT", "GCA CTCA", "TTA TGTT", "CAA GTCA", "CAA GTGA", "GAAA CTA", "TAAA TAAAA", "TCTT AAAA", "GTT GGAA", "GTT CTAA", "CCA CTC", "CA GTGAA", "GAAA GG", "GCA CGA", "TAA CTTTT", "GTT GTTA", "TCA GTTA", "CGGA TG", "TATT TGAA", "CC CTGAA", "GCC CTC", "CTT CTAA", "TTTG TTTT", "GA GCTGA", "CTG TGGG", "CAA GATT", "GAA GCTT", "TGA GTAA", "CTT GCTG", "GGA TGGG", "CGTA TG", "TCCA TTA", "GTC TGCA", "GCCA TTTT", "GTT GTAA", "CACA CAA", "GGACTA CAGG", "C GTTTTA", "TCTT CC", "TAA CCTT", "CTT TAAAA", "TGAA TTTT", "CTA CAGA", "GCAA GAA", "TAA CAAAA", "CAATT AAA", "CCA CTCA", "CATG GTGAAA", "CCCA GAA", "CTA CATT", "CC GAGG", "TCCA GTG", "TGA GTTA", "GGA GTCA", "TAA CGA", "GA GTAAA", "GA CTCTG", "GGA GCTT", "TA CTCC", "CTG CATG", "GC TTTTTT", "GTC TAAA", "GTG CGG", "CA TCTCA", "TGA TCAA", "GGA GATT", "GC AAAAAA", "CA CCAAA", "TGA CGG", "CAGA GG", "GTT GATG", "CTT GTCA", "TCCA CCTG", "GGA GCAA", "CAA GTAA", "CCA TAAA", "GTG CATG", "GCA TATT", "GTA GATT", "GCC TAA", "CTCAA AAA", "GGA GAAAA", "CTA TCC", "TAATA TTA", "GTG CTC", "CAA TATG", "TGTG GAA", "TGA CTC", "GTG TATG", "TTTTAA TG", "GC TCTAA", "CACAA TG", "CA GCTCA", "GTT GGTT", "CTAAAA TT", "GTC TATG", "TGTG AAAA", "CTG GGTT", "CCCC TCC", "CC CTCTT", "GCA GGGA", "GAAA CCA", "CATT TCC", "GCA GCCA", "TCA TATG", "GCA GGCA", "C GTAAAA", "TGA CCTG", "CAGA GGTT", "CTT GTGA", "TTA TCTT", "CTG TATG", "GTCAA TG", "GGA CGG", "GC GTAA", "CAAA CTA", "TAAA TGTT", "CTT CGG", "CTCC CCA", "TACAA TG", "TCTG TAA", "GAA TATG", "GC GGGA", "GGA CATT", "TTA TGAA", "GGA TGTT", "GGA CATG", "TCA GGTG", "CAA CAAAA", "GAAA GAGA", "GTG GATG", "GG GCTA", "CCA TCAA", "CA GCTGA", "CTC CACC", "CAA TCAA", "GTG GTC", "TGA CAGG", "CCA TTCA", "GTCC CTG", "CAGA CACA", "GTT GGTG", "CC TCCTG", "GAA CTGA", "TATT CATT", "GCC CATG", "CAA TCTT", "GAAA GCA", "GAA TCTG", "TTA TTTTA", "GTT TGGA", "TTTT TGTT", "GGGAA TG", "GC GACA", "TAAA CTG", "CCA TATT", "GGA TCC", "CAA GCTT", "TAAAAAA AAA", "TCA CTC", "CA CTGTT", "TGTTAA TT", "GGA CTGA", "GGA GTGA", "CATA CACA", "GTT TGTA", "TCCA GCA", "GTG CATT", "GG AAAAAA", "CCAA GAA", "TCAA TA", "CTT CCCA", "TGA GAAAA", "GGCC TCCCAAA", "CAA GCTG", "GCC CAAA", "TGA CTTA", "CA GCCTT", "CTG GATT", "TTTT TTTA", "TCA CGG", "GCA GTTA", "TGA CTAA", "TTA CAGG", "TGA TATG", "TAA TTATT", "TCTT GAA", "GCC CCTT", "GTT CAGA", "CTC TATG", "CCA TGGA", "GAGG GAA", "GGA GGCA", "CTT TGCA", "TCTT GG", "GGA GGTT", "GCCAA TG", "CTG GTGA", "CAA CCAA", "CCA GTC", "CTT GAGA", "TACA GCA", "CTT GTC", "GA CGGA", "CTT CTTTT", "GTG GC", "GAGGA TG", "CAA TAAAA", "GAAA TTTT", "AAAA AAAAAA", "CTC TATA", "GTA TGAA", "CTT GTTA", "TAA CATA", "CAAA CACA", "TGATT AAA", "GCTC TGTT", "GTG GGTT", "GTT GGGG", "GTG TGTA", "GTAA TTTT", "GTA TCC", "TGTGTGTG TGTG", "TCTT CCTT", "TCA CTAA", "TCTCC AAA", "TA TCAAA", "TGA TGGG", "GGA TATT", "CAAA TTTT", "GTT CAGG", "GTG GATT", "GTG CAGA", "GCTG CC", "CTCA GAA", "GCA GTC", "GGA TAAA", "GCC TTCA", "CCA GGTG", "TA TCTC", "CAA TGCA", "CCCA CTG", "GTG TATT", "CGA CAGA", "TGA GATA", "CCA GGTT", "TGTT TAA", "CATCA TG", "TGA TTCA", "GCAA TTA", "GAAA TGAA", "CTT GGTT", "GAA GATT", "GGA TTAA", "CC TCATT", "GGCCA GGCTG", "GCTA TTA", "GCCA GCA", "GAGA CAGG", "CTT GAGG", "CA GTCTT", "GTT CTCC", "TATT TCAA", "TGA CGA", "CATG AAAA", "CATTA TG", "TAAA TTTA", "GA GTGAA", "CAA CAGG", "TAA GCTT", "CACA TTTT", "GA TCTCA", "TA GTCC", "GACC CTG", "TAA TGCA", "TAA GTC", "TAA TAATT", "GAA GTAA", "CAA CTC", "CA TCATT", "GA CGAA", "GAAA CAAA", "TATT TCTG", "CATTAA TT", "CCA CCCC", "TAATA TTTT", "GTT TAAAA", "GTA TCTG", "GTCAA AAA", "GATG CTG", "TGTT CTG", "GG TCAAA", "GTA GGAA", "GTA TATG", "TGA TCTG", "GGGG CTG", "GCA TCAA", "GCCAA AAA", "CCA CGA", "GC TAATG", "CAGA GAAA", "CCTT CTG", "TCC TCTA", "GCA GGTT", "CTCA CTG", "TAGA TTA", "GCC GAGA", "CCA TCCA", "CTT TACA", "GTA CATG", "GCA CCAA", "CTT TGTA", "CTA TGTG", "TCA CTTTT", "TGA GTC", "CAA GAAAA", "CTGA CTG", "GTTTT TTTT", "GCA TAAA", "TAA TCTG", "GAA AAAAAA", "CAGGA TG", "TGA GCCA", "GAA TTCA", "TCA GACA", "GTT CCAA", "TCA GGTT", "CAAA CTG", "CATT TCTT", "TGTT AAAA", "CCA GACA", "CAA GTTA", "CATG TTA", "CATT CTA", "TCTTTT TG", "TGA GGGG", "CACA TTA", "TAAAA TAAA", "GCA TATA", "TGTT CTA", "GAA GGGG", "GAGTG TG", "TAA GACA", "GAA CTC", "CCA GTAA", "GAGA GAGG", "GC GACC", "CAA TTCA", "CGG CTG", "CCA GATT", "CCTG GG", "GGAA GAAA", "GAGA GG", "TCAAAA TG", "CCTCA TG", "TAAA GG", "CTT TGGA", "CCA GGGA", "GTA CAGA", "CTGAGGCA GGA", "TGTT TCTT", "CCA GGCTG", "CTGA GG", "GAGG CTG", "CTCC TGGG", "GAA GTC", "CGA CC", "GGA CTCA", "GGA GTC", "CA CAATT", "GTG TTCA", "GA CTAAA", "GTCA TTA", "CAAAA TTA", "TGAA GAAA", "GCA CCTT", "GTT TGCA", "TCC TGCC", "GTA GATG", "GCC TGCA", "GA GTTAA", "TCC CTTA", "GTG GTTA", "TC GGGA", "TACA TAA", "TCTC TCCA", "CA CTAAA", "TATATATA TATA", "GTG GCAA", "CACCA TG", "TTTG AAAA", "CACA CTG", "CTT GGTG", "TACA CTG", "CC TCCAA", "CAA CCTT", "CA GCCAA", "TTTT CAAA", "TGA TAGA", "TACA CTA", "TCTG GG", "TCC CAGCA", "TAGG AAAA", "CTT GGGG", "TC TGTGAA", "CC TTATT", "CATT TAAA", "TTTTA TTTTA", "GCC CTCC", "CTGA GCA", "CC CGTG", "GTA GTGA", "TCC TATT", "GAA GGTG", "TGTG CTG", "TCCA CTG", "TAA TCTA", "TGA TGTA", "GTG GTAA", "TAA TGGA", "GATG AAAA", "GTA GTAA", "GTG GGGA", "GTG TCAA", "CAGA CTG", "TC GAAAA", "CTCA TTA", "TAA TAATA", "CTCA GAAA", "CA TCCTT", "CC GCTT", "GGAA GG", "CC GTGA", "CCA CTCC", "CTA GAGA", "TAGAA TG", "GGA TTTA", "TTAA TTTT", "GC TAATA", "TCC CCCA", "CAAA TATT", "GA TCATG", "TCTTAA TT", "CA GTATT", "GTCTT GAA", "CC GAAA", "CTA TTCA", "TAA GATA", "CTT GCAA", "GCC CCAA", "TCC CTAA", "GAA GTTA", "GA TGATG", "CTT GATG", "CC CTAAA", "CCTG CCTG", "GACA TTTT", "CCA GCCA", "TGTGTGTG TG", "GTC TATA", "TCTC TGTT", "GTC TGTA", "TA TAATA", "CTT GTTTT", "CGC CATT", "CTCA GCA", "TACA GTT", "CAA GAGG", "GGAA GCA", "GCC TTTA", "CC CCATT", "CAA CGA", "GTCA TTTT", "CC CGCA", "CA GTTAA", "GAA TCTT", "CATG TTTT", "CC GGGG", "CTA CTGA", "TCA CGA", "TAAA TTTG", "GCC CATT", "CTC TAGG", "GGA CCTG", "TCA GGGA", "GAGA CTG", "CC AAAAAA", "GCC GG", "CCA GGGG", "TCA GAAAA", "CA TCTGA", "TCTT CAAA", "CTA CAGG", "GAGG CAGG", "CATT GTA", "TAAA TCAA", "GA CTCTT", "CTGA TTA", "GCA TATG", "GGA CCTT", "CAA GACA", "TATT TATG", "TATTTT AAA", "CC GAGA", "TCA TTTTA", "CTCA CTCA", "CCA CCCA", "CTC TAGA", "CTA CATG", "GTG CTTA", "CAA CCTG", "TC TGTGTT", "TAAA TATG", "CAAA GG", "CC CTGTT", "GTT CGG", "TGA TAAAA", "CA CGAA", "GTT GAGG", "CAGA GTGA", "GAAA TTAA", "CACA TA", "GAA CAGG", "TCTCC TGA", "CC TGAGG", "GGAGG CCAA", "GTT TACA", "TAA CAGG", "TGTG GTG", "GCCTCC CAAA", "CCA TCCTG", "GATT CTT", "GAA TGGA", "GTA GTCA", "CTCC TCTG", "GAAAGAAA GAAAGAAA", "CC CTGTG", "CAGTA TG", "GC GATA", "GGA CTC", "GAAA GA", "TGTT GG", "GTA GCTT", "CA TTTTAA", "CC CTCTG", "GCA TTCA", "CGA TTA", "TCA CATA", "TAA TGAAA", "GGAA TTA", "CTG TCAA", "TAAATT AAA", "CAA GTC", "GTA TTCA", "GGCCA TG", "CTT TAGA", "TGTT TCC", "CATG TA", "GAA TAAAA", "CAA CTAA", "TCA TCTA", "CA CTCTT", "CA GTTTG", "CA TAAAAA", "GCA TGCA", "GATT TA", "GAA CCAA", "TCTG TGA", "TCA GCCA", "TCTC CACA", "TCTCA GCTCA", "TATCA TG", "GCA CTTA", "CGC CAGG", "CGG GG", "CATTAA AAA", "TTTG TTA", "GGA TATA", "TC GACC", "TAA TCCA", "CC GC", "CATT GTT", "CCA GTTA", "GTA GTTA", "CTA GGAA", "CC TAATT", "TCA TGGG", "GAA CTAA", "GCTA TTTT", "CC GTCA", "CAGA TTA", "CCA TATA", "CAA CTTA", "TCA GTTTT", "CTA CCTT", "GCA CTC", "GTG TGGA", "GTG CCAA", "GACAA TG", "GA CAATT", "GTA CCTT", "TAAA CATT", "CA GGAGG", "GTG CGA", "GAAAA TTA", "TCTCTT AA", "CC GATT", "GA TGATT", "CCA TGGG", "TC GGTA", "CCA TATG", "CCA GTCC", "GCC TTAA", "TGA TCCA", "GTT GCAA", "GTA GAGG", "CAGA TTTT", "GTA CTTA", "TCTTTCTT TCTTTCTT", "GCTC TGTG", "TCAA TAA", "GTT TAGA", "GTT CGA", "CAA GGTT", "CTCA TTTT", "CACA GG", "CATG CTG", "GAA CGG", "TA TAAAAA", "GAA GGCA", "GA GCATT", "TGTT TGTG", "GCTG TTA", "GTCA CTG", "CAAA TGAA", "GTGA CTG", "GTT CTTTT", "CAGGCTG GAGTGCAGTG", "TGA TGAAA", "TAA CGG", "CTA CTAA", "GACA TTA", "GGA CGA", "GAGCA TG", "GCA TGGG", "CCA CTTA", "CTA TCAA", "GCTG TTTT", "GTC GTG", "CCTG GCC", "TCTC TGAA", "TGTT GTA", "CAGC CAGG", "GTT TAGG", "CC GCAA", "GGA GTAA", "CCAA TTA", "CAGC AAAA", "TCA TCCA", "CA CGTA", "TCA TAGA", "TAATT AAAA", "CA CTTAA", "TCTT TATT", "GAGA TTA", "TAA GAGG", "CAAA TTAA", "GA CGCA", "CA CGGA", "GTG TGCA", "TC T", "TATTA TTA", "GAAA TATT", "GGA GTTA", "TCTT TGA", "CTGA TTTT", "TGTGAA TT", "TCC CACC", "CC CTTTG", "CAA GGTG", "CAGA GTT", "CCCCA TG", "CTA CCAA", "CTCC AAAA", "CTT CCCC", "CTG CTAA", "GATT AAAA", "GC TTATG", "CTA CTTA", "TAAAAAA TT", "TCA GTCC", "CTATT AAA", "GAA TGGG", "CACA GTA", "CAA CGG", "GG TTATT", "TCA CCCA", "TGA TGCA", "TAA TTTTTT", "GTT TGAGA", "GTATT AAA", "GCC CCCA", "TATA GTA", "TA GTAAA", "TGA TACA", "GTG GTTTT", "CCA CTAA", "CACA GAGA", "CCTCTG CCTCC", "CAA AAAAAA", "CTC TCTCC", "CA TAATA", "GAA GCCA", "GTT CCCA", "TGTG TTTG", "CAA TGGA", "TGAA GTA", "CTT CATA", "CA CTGTG", "GC TCTTTT", "TGA CATA", "TAAA GAAAA", "GAGAAA TG", "CAGG GAGG", "TGTT CAA", "GA GCCAA", "GACA GAGA", "GG CTGAA", "CAAA TATA", "GTG GAAAA", "TAA GGTT", "GTGA TTA", "GGA TCTG", "GATG TTA", "GACTA CACA", "TCC TATA", "CTG CCAA", "TCC CGA", "GTGA TTTT", "GC GTTTT", "CAGA GTA", "GAAA GGAA", "CA CTTTG", "CCCC AAAA", "GCAA CCCA", "TGCA TTTT", "TCTA GAA", "TA CTTTG", "TGA GGCA", "CA TCTCC", "TC GCTA", "TGA CTTTT", "GA GCCTG", "CATT TGTT", "TCTT TGTT", "GCAAAA TT", "CC TGATT", "GA TAAAAA", "GA GTGTT", "TCC TGTA", "TACA GAAA", "TC CAGGAA", "GCCA GTG", "TAGA TTTT", "TAA TAGG", "CTCC TCA", "CATTTT TG", "CATT TCAA", "GCCA TCA", "TAAAA TATA", "GA CTGTT", "GCA TGGA", "CAAA GTT", "CA TGATT", "GA GTTTG", "CTA GCAA", "CTT CCTA", "GG GGAGG", "CTA TATG", "TATT TATTTT", "CA CCATT", "CC CTCAA", "TTTTTTTT TTTTTT", "GA TCATT", "GTA CATA", "CTC CATA", "CCCC GTCTCTA", "GCC TGCC", "CTA GCTT", "CC CGGA", "GATG TTTT", "GTA TTTTA", "TCA GATA", "CCTG GAA", "TATT CCA", "GGA CCAA", "GCCA TTA", "CGA CTGA", "TAA GCTG", "TAAA CACA", "GTT TCTC", "CA TCTTA", "GAAA TTTG", "TAA TGGG", "TAAAA TTTT", "CTG TTCA", "CCTG TTA", "TA CTGAA", "TGA CCCA", "TGA TTTTA", "CTCC TTA", "TATA GAA", "CTG CGG", "GC GGTA", "GTG CTAA", "CAGA GGAA", "TACA TCA", "TCAA TCAA", "CTG CAGCC", "TGAA TATT", "TCTA CAA", "CCA CATA", "CC CGTT", "TATA CACA", "TCC TCTC", "TCTA CTT", "CC GGAA", "CTTTT TTA", "GAAA GAAAA", "CTA TCTT", "GA CTTTG", "TGAA CAA", "GCA GTTTT", "GC TAAAAA", "GAGG CGG", "TAA TAAAAA", "CTG GTCA", "CAGA CAA", "GGA TATG", "TGAA GG", "GCCA GAA", "CCA GGCC", "CCA CCATG", "CAAA CTT", "TCA TGTA", "GCTG CTT", "GTAA TA", "CCCC CAA", "CA GCCTG", "TCAA CTT", "TAAAA TTAA", "GCTG AAAA", "CGA CGA", "GTG GGCA", "TGA GGGA", "CGC TCC", "TTTT GTTTT", "GA GTCAA", "TCA TGCA", "CTG CTTA", "TAA GTTTT", "GTA GCAA", "CCTT GG", "TGA CAAAA", "CTG GTAA", "TCTT TATA", "TGTG TGTT", "CTG GTC", "CTG GCAA", "CATT TCTG", "CTC TACC", "CTGA GGA", "CTAAAA TG", "CTA GATT", "GTA TCAA", "CA GTCAA", "CTG GGTG", "CC TCTTA", "TGA GTTTT", "TTTTA TTTA", "CC TTTTTT", "TATA TACA", "TA GCAAA", "AAA TTA", "CTG GATG", "GA TAATA", "GA CAAAAA", "CCTG GGA", "GCTT TCA", "GTA CAGG", "GCTG GAA", "CTA CTCA", "CAA TGTA", "GC GTGAA", "GA TCCTT", "TATTAA TG", "GCC CGA", "TAAA GTG", "GCTT CCA", "CATG GAA", "TGAA GTT", "CTT TCTC", "TCTGTG TG", "GTA TGTA", "CAA TACA", "TCAA GG", "CC TCTAA", "TGTG GG", "GA TCTGA", "GTA CTGA", "TTAA TTAA", "GCA GAAAA", "CTA CATA", "CC GGTG", "GGGG AAAA", "TACAA AAAA", "TTTT GG", "GTGA GAA", "TCAA TAAA", "TCAA GTT", "CTCA GGA", "CTA CTC", "CAAA TCA", "GGCA GAA", "CC CGAA", "TGTT GTG", "GAGC AAAA", "TATT TGTG", "GTA GGTT", "CTA CCTG", "CA CAAAAA", "CTCA GG", "GCTT TA", "CAGA GCAA", "CTCA GTG", "GGAA GAGA", "TAA CCTG", "GAAA TATA", "CGA GAA", "GTGA GG", "CATT TATA", "GGCA GCA", "TC TAAATT", "CCCA GTG", "GCC TAGG", "TGCA TTA", "CC GTAA", "CATT CCA", "CTA GTTA", "GA CTTAA", "CTA TACA", "GACA CAA", "TCTT CACA", "CC GGTT", "TAAA GTAA", "CTG TGGA", "TAA GGTG", "TCCA GTA", "CAAA TTTA", "AAATT AAAA", "CCA TCTA", "CTCC CTT", "CTCC TTTT", "GAGAGAGA GAGA", "GGA GATA", "CCTA TTA", "CACC AAAA", "CC GTTA", "TGTT TATA", "CTCA GGAGG", "GA CGTA", "GTCC TTA", "GAAA GTT", "GCTG GTG", "CTC TACA", "CAA TAGA", "TAAAA TATT", "GTA CCTG", "GTA CTAA", "CTT TGAAA", "CCTT TCC", "TAAAAA TTA", "CTC GG", "CAA GATA", "CATT TGA", "CACC TCA", "GCCA GCC", "GTC GG", "GCA CATA", "CA CTCAA", "CTTTT AAAA", "CAGGAA TT", "GCC TATT", "TCTT TCTG", "CTGAGGCA GGAGAA", "CAGG CAGG", "CTA GTAA", "TCCA TA", "GAA CTTA", "C G", "GCTG TGA", "GAAAA TA", "TCTT CATT", "GAGG GAGA", "CCCA TCC", "GAGG TGGG", "GCC TCTA", "GTA GGTG", "TAAA CCA", "GAA GGAAA", "TATT GG", "A TG", "TCCA GTT", "CCCA CAA", "GAAA CACA", "GTC TCAAAA", "CTTTT CTTTT", "TGAA GGA", "TATT GATT", "CTA TGTA", "AAAAAAAA AAAAAA", "TCCTT AAA", "GC GCTA", "TCCA CTT", "GA CTCAA", "TAAA TACA", "TCA TGGA", "TCTG GGA", "TCC TATG", "CTG TGCA", "TCAA GTGA", "TCA TAAAA", "CA TCCAA", "CCTT CCA", "CTG TACA", "GAA GGTT", "CTG TGTA", "GTCA CTT", "TCA CAAAA", "TCA GGCA", "GTGTT AAA", "CC CTTAA", "CAAA GTG", "GAAA TGTT", "CTG GGGA", "GA CGCC", "TATA TGTG", "CTA GATG", "GAAATT AAA", "GAA TGCA", "GCA CTAA", "CGG GAGG", "GCCA CAA", "CGC TTA", "TCCA CAA", "CAGA TA", "TC TGAATT", "TATTA TTTT", "GC GCGG", "CTC TGAAA", "TCTCTT TG", "TATT TCTA", "GGGG TGGG", "GGA TGCA", "CCA CACC", "TAAA TGTG", "TCTT CCTG", "GCAA GG", "CTG CTCC", "CTG GAGTG", "CTGTT AAA", "CACA CAAA", "CTGA CTT", "GAAAA GAAAA", "CCTT CTCC", "GAAA TAAAA", "CCTCA GGTGA", "GA TAATG", "GAATT GCTT", "CCAAAA TT", "CGTG AAA", "CACTG AAA", "CAGTG AAA", "GA TCTTA", "GAGA TGGG", "TCTG CCA", "TGA GGTA", "TATG GAA", "TATA TTTTA", "TGAA CTT", "GCA GATA", "CTTTT CTT", "GTAAAA TG", "TCTC TAA", "TCTG CAAA", "GA GCCTT", "TA TCATT", "CAA TTTTA", "CC GCCA", "TATT TAAAA", "GAGA GATG", "GAGA TGGA", "GCCA GGATG", "CGA GTAGCTG", "TTCA TTTT", "TATA CTT", "GTC TACA", "GTGA GTGA", "GCTA CACA", "GGGA GGA", "CAA GGCA", "GC TTTTAA", "CA CTATT", "GTT CATA", "TCC TC", "GTG GACA", "TATT TGGA", "CTC CAGTA", "GTT CAGTT", "CCAA GG", "CAGA GCC", "CTC GCC", "CC GATG", "GGAA TTTT", "TCCA GCC", "CC TCTTTT", "GAA CCTT", "CATG CACA", "GTT TC", "GAA GATA", "TA CCCC", "GCTG CCA", "GGGG GAGG", "GCAGTGA GCTGA", "CTG TCTA", "CGA GGA", "CAA TGGG", "GC TGTGAA", "GAAA GTG", "TACC AAAA", "GTCA GG", "CAGC TCC", "TGTG CTT", "GTC TAGG", "TTTT TGTA", "TTA TATG", "TCA GGGG", "TATT GTTA", "CC TGAGA", "TA TCTCA", "CAA TCTG", "CA CTCTG", "GATT TAA", "TGAA TAA", "TCTT GTA", "TCAA CTG", "TCTC CAGG", "CTA GAGG", "CTGA GAAA", "CTA GCTG", "TCCA CCA", "CGA TTTT", "CC GGCC", "GTT GACA", "CTTA GAA", "CA TAATG", "GA GTATT", "CACA GAAA", "GA CTGTG", "CTA TTTTA", "TGA GGAAA", "TTATT AAAA", "CTTA TTTA", "CAGA CTT", "CA CGCC", "GCTT GG", "CCTG CTT", "TAAA GCAA", "CCTC GTGA", "TA GAATT", "CTTA CAA", "TAAA GGAA", "GTC TAGA", "GTGA CTT", "TACA TATG", "GTCA GGA", "GCTC CAGG", "GAA GGGA", "CA TGATG", "TCA TCAAA", "CGTT AAA", "GTA CTCA", "CTCC CAA", "TATA TGTA", "GGTA TTTT", "TAA GCCA", "C GAAATT", "GTTTG TTTT", "TCTG TCTT", "TATA TCA", "TGTT CATT", "CAAA CCA", "TTCA TTA", "TATT TGTA", "GATT GAA", "CTA TAAAA", "GATTAA TT", "CCCA CCA", "TCC TAGG", "TAAA TGTA", "CTCTT AAA", "GCA GTCC", "GC GGCTG", "GTC TCGAA", "TGAA TGA", "CTG GGGG", "GTC TCGA", "GAA CAAAA", "TGAA TCA", "TGTATTTT TAGTAGAGA", "GTTA TTAA", "TTTTTT AAAA", "GTCA GTG", "CCCA TTA", "CACA GGA", "TATT CCTT", "TCTG CCTT", "CCTG GTG", "GC GAGC", "TA CTAAA", "TACA CAAA", "CC GTCC", "GCTT TGTT", "GCA TCCA", "CA TCTAA", "GC TGTGTT", "GTA GACA", "GCC TATG", "TCTT TGTG", "GATT CTG", "CGCC CGG", "GA TGAGA", "TA TCTGA", "TGAA TTTG", "CC TGATG", "TAAAA CAA", "CTT TAGG", "TTTT CCTT", "TGAA TAAA", "CGG GGA", "CAAA CATT", "GTA TGGA", "GCTT AAAA", "TA CCAAA", "CAAA GAGA", "CTCC TGCC", "GTAAAA AAA", "CACA GCC", "CCA TGCA", "TA CAATT", "CTA GTGA", "CTGA GTT", "GAGTG AAA", "TCTGTT TG", "CTG TAGG", "TATAA AAAA", "GCATT AAA", "GTC CATA", "TGTTAA AAA", "TGTT TGA", "GAA TAGA", "CTT CAAAA", "CTG GACA", "CTG TAGA", "CCATT AAA", "CTA TCTG", "CACTA TG", "TTA TCAA", "TAA GTAAA", "TAATCCCAGCACTTTG GGAGGCC", "CCA GAAAA", "TGAA GCA", "TCC CTTTT", "TCA TACA", "TA CGTT", "GCC GTG", "GGAA GTG", "GG CCAAA", "GTA CCAA", "TCTCTA CTAAAAATA", "CATT GTG", "TGTG TGA", "GAAA CAGA", "CTT GACA", "GA TGAGG", "GAGA TTTT", "CCTT CAA", "GAA TCTA", "CTC TCCTT", "GG CGGA", "TCTATCTA TCTATCTA", "CACA CAGA", "TGTG TGTA", "CAAA GCC", "TGTG CCA", "GTT GAAAA", "CTC CAGCA", "TCAA GGA", "TA GCTCA", "CGC TGA", "CCTG AAAA", "GA CTATT", "GATT CCA", "GCTT CTA", "GTC TGCC", "CTT GGCA", "TGTG GTA", "GCTT TGA", "GCTC TCTG", "CTCA CAGA", "TCTT TAAA", "CAAA GCAA", "TA CTTAA", "GCTT CAA", "CATT GAA", "GGA GGAAA", "CTA TAGA", "CTGA GGAA", "CCTG GCA", "CC CTATT", "CTC GTG", "TTA CACA", "TTA GGAA", "CTG GTTA", "GTT GTCC", "TAATG AAAA", "TATT TACA", "GG GAATT", "GTA GTTTT", "GCTG CAA", "CTA CGG", "GCC GGA", "CTG GGCA", "CCTT AAAA", "GATG GAA", "TAGATAGA TAGATAGA", "TATG TAA", "GTA CGG", "TATT CAAA", "GA TCTCC", "CCTG TTTT", "TATT GCA", "GGAAGGAA GGAAGGAA", "GG TAATT", "TTA CAGA", "TCA GC", "GCAAAA TG", "GAGA GCA", "GTA GAAAA", "CATT TGAA", "TCTT CTTTT", "TCC CATA", "GTTA TTTA", "CTA TCTA", "CA TCCTG", "TCTT GTG", "TTA TTATT", "CC CGTC", "TACTA TG", "TAAA CATA", "TAA GGAAA", "GCTT GTG", "CTC TAAAA", "GTTTT AAAA", "GACA GGA", "TCC TAGA", "TCCA CCCA", "GTT TGAAA", "CCA TCTCA", "CTAA GAA", "GTA TCTA", "GTGA GGA", "GCTG GAGG", "CCTGTAA TCCCAGCTA", "GCAA CAA", "CTT TCAAA", "CAAA TGTT", "CTT GTCC", "TCTCAA AAA", "TATT TATTA", "TAA GGCA", "GAGA GGAA", "TA TGATT", "GCA TCTA", "C GTTATT", "GCC TGTA", "GTT TCAAA", "CCTTCCTT CCTTCCTT", "GG CTTTG", "GTCA GAA", "CATG CATG", "GTCA TTTA", "CTG GAAAA", "CTT CGA", "CCTA TTTT", "CCAA CAA", "TCCA TCC", "TAAA GTTA", "GTC TCTC", "TAA TCAAA", "GATTTT TG", "GATT TCTT", "GG GCTGA", "GCA TGTA", "CCTG GGTT", "GAGA CAA", "GCTG TCA", "TGA TAGG", "GGA GACC", "CC GGCA", "TAA TCTCA", "TGAA TTAA", "TCTG GTG", "GCC TC", "GG CGCA", "CCA GCTA", "CA GTCTG", "TGAA CTA", "GTAA GAA", "CCTT TCA", "TCCA TGA", "CAAA GGAA", "CTC TC", "CTC TCTCA", "CTC CAGC", "GTA GATA", "CCCC CTCC", "GG CGCC", "TCTG TCC", "GA CCATT", "CTT GAAAA", "TTA TCC", "TACA TGTG", "CAAA TTTG", "TTTT GTG", "CAGA GTG", "GTAA TAA", "GTGA GTG", "TTTT TCC", "GG CTCTG", "GCC CTAA", "GG CTGTT", "CC CAATT", "CAGA GCTT", "TATAAA TG", "GA GTCTG", "TCTTAA AAA", "GTTTTA TG", "GA TCCAA", "GGCC CTG", "GA TCCTG", "TCAA GTG", "GATT CAA", "CCTC TCTT", "GAGA CGG", "CAGA TCA", "TAAAA GAA", "CTGA GCAA", "CCTG CCA", "CCTT CTA", "CGC TCA", "GG CTGTG", "TGGG AAAA", "GGA GCCTG", "CTGA GTG", "CGTC AAA", "TCAA GTA", "CGTAA TT", "TTA CTTA", "TATA CTA", "GG GCAAA", "CAA CTTTT", "CTT TGCC", "GC CAGGAA", "CACA CTA", "GCC CAGC", "TAAATAAA TAAATAAA", "CTT TCCTT", "GGGA GAA", "TATG GTA", "CGG CCA", "CCTC TCTG", "GAAA GCAA", "CAA GCCA", "GG CGTT", "CTC TTTTA", "TCGGCC TCCCAAA", "GATT TATT", "CAA GTCC", "TA TCTTA", "GTTCAA GACCA", "CTCA CACA", "GAAA TCAA", "TGA GACC", "GG GTAAA", "GCTT GTT", "GA TTTTAA", "TTTT TATA", "CAGA GCTG", "TC TGTTAA", "GTAA TTAA", "TCTT TGAA", "CTT GCCA", "TTTT CATT", "CCA TGTA", "TCTC GGCTCACTGCAA", "GGA TTCA", "TC TATTAA", "TACA TAAA", "GATT GATT", "GGA GAGGA", "CGC AAAA", "GGA CTAA", "TTA TGTG", "GTCA CTCA", "GACA GCA", "CGA GTT", "GATG GTT", "GGAA GAGG", "GCCAA CATGGTGAAA", "GGA GCCA", "TGAA CTG", "CCTC TGTG", "GTA TAAAA", "TCC CAGAA", "CATT TATG", "GA TTATG", "TGTT TCTG", "GAGTG GGTT", "TACA TATT", "CTC CAGGA", "GACA CTG", "GG TCTCA", "CC GGGA", "TGTT TAAA", "CTCA CCA", "GGA CTTA", "GCC CACC", "CAAA TCAA", "GAAA TGTG", "TA GTTAA", "TCTA TAA", "TTA GATT", "GTG TAGG", "TACTG AAA", "GCA CCCA", "GTG GGCTG", "GAA TGAAA", "TCTA GTT", "TCA GGAGA", "TCCA CTA", "CTCA GTT", "TACTT AAA", "GA CTCCA", "TCCATT TG", "CACA GCAA", "GCTCATG CCTG", "GGTG CTG", "GCTT TCTT", "GTG GCCA", "TA CGTG", "GTG CAGTG", "TGAA GTCA", "CCTT TAA", "TCTCAGCTCA CTGCAA", "GAAA TATG", "CC TCAAAA", "GGGG CGG", "CGA CAA", "GG TGATG", "GTCTT AAA", "CAGAAA TG", "CGTCA TT", "CCAA GCA", "GGA TCAA", "GTGCTG GGATTA", "GCTG GCC", "CGGA GCTT", "TACA TGA", "TGTT TGAA", "TCTC CATT", "TAA GCAAA", "CCTT TCTT", "TA CTGTT", "TCCA TCTT", "CTTA CTT", "CGGA GGTT", "CAAAA CAA", "TCA TAGG", "TTA CTAA", "CTTA TTTG", "GAA TGTA", "CCCCA TGGA", "TTA CTGA", "CGG AAAA", "CTC CAGTG", "TGTT CCA", "CAGA TGAA", "GTT GATA", "TCC CCCC", "CATT GCA", "CTCA GCC", "CTTA CTG", "TA TCCTT", "CTTTTA TG", "TGAGTA GCTG", "GACTG AAA", "CAA TGAAA", "CGA CTG", "CTT GGGA", "GCAA GCA", "TCA CTCC", "GATT TGA", "CATTTT AAA", "TCAA CTA", "GTCC AAAA", "CACC CTG", "TTA CCTT", "CAA GGGG", "TTTT GGA", "GTTA TTTG", "GCTA CTG", "CTGAGGCAGGA GAATG", "GTGA TGA", "GTA GTC", "TAGTA TG", "GTA TAGA", "GTG TCTA", "GCTG CTA", "TTA GTAA", "TAAA CATG", "GTCA CCA", "CA TCTTTT", "CATA TAA", "TCTC TCTA", "TTTTA TTAA", "TATT CTAA", "GAAA TTTA", "CTT CCCTG", "TAAA GATG", "TA CGTA", "GTT TATTA", "GAAAA GAA", "CCCA CCCA", "CAATT AAAA", "CC GACA", "CAAA GTGA", "CAAA CAAAA", "GCAA TTTT", "CGATT AA", "TTA GAGA", "CTGA TGA", "GGA GGAGG", "GTCC TGGG", "TCA TGAAA", "GCAA CCA", "GTT GGCA", "GCGG CGG", "GTCC CCA", "GTA GGGG", "GCCA TGTT", "GTT CGAGA", "GCC TATA", "TAAA TTCA", "GG CCATT", "GAAAA CAA", "TGTG TATG", "GTA CTC", "TAGG GAA", "CCTT GAA", "TC TATTTG", "GAGG GCA", "GAAA CTGA", "TA CGC", "TA CAAAAA", "TCA TTATT", "GGAAAA TT", "TCAA TATT", "CC CGTA", "GGA GAGAA", "TTA GTTA", "CTCA GAGA", "TC GAGC", "CTA GTCA", "GATG GCA", "TGAA CATT", "CTA TGGG", "CACA CCA", "TCAA TTAA", "GGAA CTG", "TTA CATG", "CTT TCATT", "CAGC TCTG", "TCTTTT TTTT", "TAAA TCTT", "TGA TCTA", "CATA CAA", "GC TCAAAA", "GC TGTGTG", "TCAA TCA", "GATT TGAA", "CCAA GGA", "GTCC TCA", "GTG CTCC", "AAAA TAA", "GTGA CAA", "GCTCA CGCCTG", "CGA CGG", "TA TCCAA", "CACA CATG", "TCTC TCTCC", "TGTG GTT", "CTT GGTA", "TCTG GTT", "TTTA TAA", "CTG CTTTT", "TGTG TCA", "CACA TCA", "CC TAATG", "C GTTTTTT", "GCTG GCA", "GA CGTC", "TATAA TTA", "TACA GTAA", "GAAA GTAA", "GTC TGAAA", "CCCA TTTT", "TATA TGA", "CTT GATA", "CTT TATTTT", "CTT TATTA", "GG CGAA", "CCA TGCC", "CCTG CCTT", "GAAGAA GAAGAA", "CTGA CTGA", "GCC CTTA", "TA TCTAA", "GTG TTTTA", "TGTG GCA", "TATT GTAA", "GCCA GAAA", "CCCTG TCTC", "CACA GGAA", "AAAA CAA", "AAAAAAAA AAAAAAA", "TAA CTCC", "GCC TAAA", "CGA GTA", "TA GTATT", "GTATTTT TAGTAGAGA", "GCTG CAGG", "TATT GAAA", "CCAGCC TGGG", "GCTCC AAA", "TA CGAA", "GGCC TCC", "TATA CAAA", "CATG GCA", "CATG CAA", "TACA CCA", "CTT TACCA", "TACA GAGA", "TATT CTTA", "TATG TCA", "TCAA GCA", "TCAA TGA", "GG CTCTT", "GGAA GTT", "TCCA TGTT", "GCTT TCC", "TATG TGA", "GTG TAGA", "TTTT TAAAA", "GCTG GAGA", "GTGA GAGA", "CCTA GAA", "CCTCC AAA", "CCAA TGA", "CAGG GCA", "CTA TGCA", "CTT CACC", "CTA CAAAA", "CTCA CC", "GAGTA TG", "TA GAAAAA", "CTTTT GAA", "TAAA GAGA", "CATG TCA", "TCTTTT AAA", "CACA GTGA", "GA TCTAA", "TAA GGTA", "CATA GAA", "CGC GCC", "CAGC TTA", "TATA GTT", "CGG GCC", "TATC CATT", "TGTTTG TTTT", "GCTG GCTG", "TACA GGA", "CTCC TTTG", "CAA TCTA", "CCCC CTG", "TATA CTG", "CTGA GCC", "CGG TTA", "TGAA GTG", "GCTT CCTT", "TTTTA TTTG", "TA GTGAA", "CTGA GGTG", "TCTT CTC", "GACA GAAA", "CTGAA CTGAA", "CCTG GGAA", "TCC CCAAA", "TATG TATT", "GATT TCTG", "CATT CAAA", "CACA GTT", "GCTT GAA", "GTG GATCA", "CTGA GTGA", "TGAA TTTA", "TCAA CAAA", "GG TCATT", "GTAA TTTA", "GC GACTT", "CTGA GAGA", "GTG CCCA", "CTA GGTT", "TCC TGAAA", "GTC CACC", "TCA CAGAA", "GC GAAAA", "GTA TGGG", "TGAA CAAA", "TAAA CAAAA", "CC GTTTT", "TC TCAATT", "TCCA GAAA", "GTAA CAA", "GCA TTTTA", "TCTC CATG", "TTA TAAAA", "CAGG CAA", "CTAAAA AAA", "GTT GGGA", "TAAA GATT", "TGAA GAGA", "CCCC TCA", "TGTT TATG", "TCTA CTG", "CCAA TTTT", "GGTG GTG", "GGAA CAA", "TGTG GGA", "TCTG CTA", "GAA CGA", "GTAA GTA", "GTT GCCA", "AAAA TTTT", "GC GCGA", "GAAA GATG", "GTC TCTCA", "TCCA TCAA", "GCA GCTA", "CACA TTTG", "CTGA CAA", "TCCA CC", "GC T", "CCCA CTT", "GCA GGTA", "GAGG CCA", "TAAA GTCA", "CTG GATA", "CGG CAA"]}}