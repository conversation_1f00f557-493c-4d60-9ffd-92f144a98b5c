#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动服务器脚本 - 安全CPU模式
完全禁用CUDA，确保所有操作都在CPU上进行
"""

import os
import sys

def main():
    # 在导入任何PyTorch相关模块之前设置环境变量
    os.environ['FORCE_CPU'] = 'true'
    os.environ['CUDA_VISIBLE_DEVICES'] = ''
    os.environ['CUDA_LAUNCH_BLOCKING'] = '1'

    print("=" * 60)
    print("🚀 启动 RNA 亚细胞定位预测服务器 (安全CPU模式)")
    print("=" * 60)
    print("⚙️  强制使用CPU进行预测")
    print("🔒 已完全禁用CUDA设备")
    print("🛡️  安全模式：确保无GPU依赖")
    print("🌐 服务器将在 http://localhost:5000 启动")
    print("=" * 60)

    # 现在导入PyTorch并验证CUDA已禁用
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")

        # 强制禁用CUDA相关功能
        torch.cuda.is_available = lambda: False
        torch.cuda.device_count = lambda: 0
        torch.cuda.get_device_name = lambda x: "No CUDA device"

        print("✅ CUDA功能已强制禁用")

        # 测试CPU张量操作
        test_tensor = torch.randn(2, 2)
        print(f"测试张量设备: {test_tensor.device}")

    except Exception as e:
        print(f"❌ PyTorch导入或测试失败: {str(e)}")
        sys.exit(1)
    
    # 导入并运行主应用
    try:
        from app import app, initialize_predictor, logger
        
        # 初始化预测器
        logger.info("正在初始化预测器 (安全CPU模式)...")
        initialize_predictor()
        
        # 启动Flask应用
        logger.info("启动Flask API服务器 (安全CPU模式)...")
        app.run(
            host='0.0.0.0',  # 允许外部访问
            port=5000,       # 端口号
            debug=False      # 生产环境关闭debug模式
        )
        
    except Exception as e:
        print(f"❌ 服务器启动失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
