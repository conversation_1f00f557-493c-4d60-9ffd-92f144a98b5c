<template>
  <div class="min-h-screen section-padding">
    <div class="container-custom max-w-5xl">
      <!-- Header -->
      <div class="text-center mb-16">
        <h1 class="text-4xl md:text-5xl font-bold text-neutral-900 mb-4">
          Help & Documentation
        </h1>
        <p class="text-xl text-neutral-600">
          Learn how to use RNALoc-Att effectively
        </p>
      </div>

      <!-- Help Content -->
      <div class="card p-8 mb-12">
        <!-- Table of Contents -->
        <div class="mb-10 p-6 bg-neutral-50 rounded-apple">
          <h2 class="text-xl font-bold text-neutral-900 mb-4 flex items-center">
            <ListBulletIcon class="h-6 w-6 mr-2 text-primary-600" />
            Table of Contents
          </h2>
          <ul class="space-y-2">
            <li v-for="(section, index) in helpSections" :key="index">
              <a 
                :href="`#section-${index}`" 
                class="flex items-center text-primary-600 hover:text-primary-800 transition-colors"
              >
                <ChevronRightIcon class="h-4 w-4 mr-2" />
                {{ section.title }}
              </a>
            </li>
          </ul>
        </div>

        <!-- Help Sections -->
        <div class="space-y-12">
          <div 
            v-for="(section, index) in helpSections" 
            :key="index"
            :id="`section-${index}`"
            class="scroll-mt-20"
          >
            <h2 class="text-2xl font-bold text-neutral-900 mb-6 flex items-center">
              <component :is="section.icon" class="h-6 w-6 mr-3 text-primary-600" />
              {{ section.title }}
            </h2>
            
            <div class="prose prose-lg max-w-none text-neutral-600">
              <p v-html="section.content" class="text-justify"></p>
              
              <!-- Conditional content based on section -->
              <div v-if="section.type === 'fasta-format'" class="mt-6 bg-neutral-50 p-6 rounded-apple font-mono text-sm">
                <div class="text-neutral-900 font-semibold mb-2">Example FASTA Format:</div>
                <pre class="whitespace-pre-wrap text-neutral-700">{{ fastaExample }}</pre>
              </div>
              
              <div v-if="section.type === 'steps'" class="mt-6 space-y-4">
                <div v-for="(step, stepIndex) in section.steps" :key="stepIndex" class="flex">
                  <div class="flex-shrink-0 h-8 w-8 rounded-full bg-primary-100 text-primary-600 flex items-center justify-center font-bold mr-4">
                    {{ stepIndex + 1 }}
                  </div>
                  <div class="pt-1">
                    <p class="text-neutral-700 text-justify">{{ step }}</p>
                  </div>
                </div>
              </div>
              
              <div v-if="section.type === 'faq'" class="mt-6 space-y-6">
                <div v-for="(faq, faqIndex) in section.faqs" :key="faqIndex" class="border-b border-neutral-200 pb-4">
                  <h3 class="text-lg font-semibold text-neutral-900 mb-2">{{ faq.question }}</h3>
                  <p class="text-neutral-600 text-justify">{{ faq.answer }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Help -->
      <div class="card p-8">
        <h2 class="text-xl font-bold text-neutral-900 mb-6 flex items-center">
          <QuestionMarkCircleIcon class="h-6 w-6 mr-2 text-primary-600" />
          Need More Help?
        </h2>
        
        <div class="flex justify-center">
          <div class="text-center p-6 max-w-md">
            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <EnvelopeIcon class="h-8 w-8 text-primary-600" />
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 mb-4">Contact Support</h3>
            <p class="text-neutral-600 mb-6 text-center leading-relaxed">
              If you have any questions, encounter issues, or need assistance with using RNALoc-Att, our support team is here to help. We're committed to providing you with the best possible experience.
            </p>
            <router-link to="/contact" class="btn-primary px-6 py-3">
              Contact Us
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import {
  ListBulletIcon,
  ChevronRightIcon,
  ServerIcon,
  DocumentTextIcon,
  QuestionMarkCircleIcon,
  EnvelopeIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
  ClipboardDocumentListIcon
} from '@heroicons/vue/24/outline'

const fastaExample = `>NM_014041
GCCGCCATCGCTCTCCCGGGCTTAGAAGGCCCGGCTACTGACGCGCAGTGCCAGACCTTACCCCTCACGGTCCTTAAGTCTCGGTCGCCCTCGCCTCGCAGCCTGCCAC...`

const helpSections = ref([
  {
    title: 'Getting Started',
    icon: InformationCircleIcon,
    content: 'RNALoc-Att is a web-based platform for predicting the subcellular localization of mRNA sequences. This tool uses advanced deep learning algorithms to analyze mRNA sequences and predict their most likely cellular compartment destinations with high accuracy.',
    type: 'basic'
  },
  {
    title: 'How to Use the Prediction Tool',
    icon: ServerIcon,
    content: 'The Prediction page allows you to submit mRNA sequences for analysis. Follow these steps to get prediction results:',
    type: 'steps',
    steps: [
      'Navigate to the "Prediction" page from the main navigation menu.',
      'Enter your mRNA sequence in FASTA format in the provided text area.',
      'You can click the "Example" button to see a sample mRNA sequence in the correct format.',
      'Click "Submit" to send your sequence for analysis.',
      'Wait for the processing to complete. You will be redirected to the results page.',
      'Review your prediction results, which include the predicted subcellular location and confidence score.'
    ]
  },
  {
    title: 'FASTA Format Requirements',
    icon: DocumentTextIcon,
    content: 'mRNA sequences must be submitted in FASTA format. The FASTA format begins with a description line (header) starting with ">" followed by an identifier and optional description. The sequence data follows on subsequent lines.',
    type: 'fasta-format'
  },
  {
    title: 'Submission Guidelines',
    icon: ClipboardDocumentListIcon,
    content: 'Please follow these guidelines when submitting mRNA sequences for analysis:',
    type: 'steps',
    steps: [
      'Each submission is limited to 100,000 nucleotides or less.',
      'Each mRNA sequence must start with a greater-than symbol (">") in the first column.',
      'The words after the ">" symbol are used for identification and description.',
      'The query mRNA sequence should be no shorter than 8 characters.',
      'Only A, C, G, T characters are accepted in the sequence.',
      'If a query sequence contains any illegal character, the prediction will be stopped.'
    ]
  },
  {
    title: 'Understanding Results',
    icon: InformationCircleIcon,
    content: 'After submitting your mRNA sequence, the results page will display the prediction outcome. The results include the sequence ID, length, predicted subcellular location, and a confidence score. The confidence score indicates the reliability of the prediction, with higher values representing greater confidence.',
    type: 'basic'
  },
  {
    title: 'Troubleshooting',
    icon: ExclamationTriangleIcon,
    content: 'If you encounter any issues while using RNALoc-Att, here are some common problems and solutions:',
    type: 'faq',
    faqs: [
      {
        question: 'Why is my submission failing?',
        answer: 'Check that your mRNA sequence is in the correct FASTA format and only contains valid characters (A, C, G, T). Also ensure your sequence is not longer than 100,000 nucleotides.'
      },
      {
        question: 'The prediction is taking too long. What should I do?',
        answer: 'Because our cloud servers perform inference using CPUs, processing very long sequences may take some time. If the process seems stuck—such as when the page displays an HTTP 504 error—try refreshing the page or submitting a shorter sequence.'
      },
      {
        question: 'How accurate are the predictions?',
        answer: 'RNALoc-Att has been trained on extensive datasets and achieves high accuracy. The confidence score provided with each prediction indicates the reliability of the result.'
      },
      {
        question: 'Can I analyze multiple sequences at once?',
        answer: 'The web server supports batch prediction of multiple mRNA sequences. However, since the deployed cloud server uses a CPU for inference, the prediction speed is relatively slow, and processing too many sequences in a batch may exceed memory limits. Therefore, large-scale batch prediction is currently not supported.'
      }
    ]
  }
])
</script>
