# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Flask API服务器 - mRNA亚细胞定位预测
# 基于训练好的深度学习模型提供RESTful API接口
# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

import os
import yaml
from argparse import Namespace
from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
from pipeline import Pipeline

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
# 启用CORS以支持前端跨域请求
CORS(app)

# 全局变量存储预测器实例
predictor = None

@app.route('/', methods=['GET'])
def welcome():
    """根路径欢迎页面"""
    return jsonify({
        'success': True,
        'message': 'RNA亚细胞定位预测API服务',
        'version': '1.0.0',
        'endpoints': {
            'health': '/health',
            'predict': '/predict (POST)',
            'labels': '/labels'
        },
        'usage': {
            'predict': 'POST /predict with JSON: {"sequence": "AUGCUGAUC..."}',
            'health': 'GET /health for service status',
            'labels': 'GET /labels for all possible labels'
        }
    })

def initialize_predictor():
    """
    初始化预测器实例
    加载模型配置和权重
    """
    global predictor
    try:
        # 获取当前文件目录
        BASE_DIR = os.path.dirname(os.path.abspath(__file__))
        
        # 加载配置文件
        exp_dir = os.path.join(BASE_DIR, 'exp10')
        cfg_path = os.path.join(exp_dir, 'config.yaml')
        
        if not os.path.exists(cfg_path):
            raise FileNotFoundError(f'配置文件未找到: {cfg_path}')
        
        # 读取配置
        cfg = yaml.load(open(cfg_path, 'r'), Loader=yaml.FullLoader)
        cfg = Namespace(**cfg)
        
        # 设置模型路径
        cfg.ckpt_best_path = os.path.join(exp_dir, 'checkpoints', 'best_model.pth')
        cfg.threshold = 0.5

        # 设置设备选项（可以通过环境变量控制）
        force_cpu = os.getenv('FORCE_CPU', 'false').lower() == 'true'
        cfg.force_cpu = force_cpu

        # 创建预测器实例
        predictor = Pipeline(cfg)
        logger.info("预测器初始化成功")
        
    except Exception as e:
        logger.error(f"预测器初始化失败: {str(e)}")
        raise e

@app.route('/health', methods=['GET'])
def health_check():
    """
    健康检查接口
    返回API服务状态
    """
    return jsonify({
        'status': 'healthy',
        'message': 'mRNA亚细胞定位预测API服务正常运行'
    })

@app.route('/predict', methods=['POST'])
def predict():
    try:
        logger.info("收到预测请求")
        
        # 检查请求内容类型
        logger.info(f"Content-Type: {request.content_type}")
        logger.info(f"Request data: {request.data}")
        
        # 获取JSON数据
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': '请求必须是JSON格式'
            }), 400
            
        data = request.get_json()
        logger.info(f"解析的JSON数据: {data}")
        
        if not data or 'sequence' not in data:
            return jsonify({
                'success': False,
                'error': '缺少sequence字段'
            }), 400
            
        fasta_sequence = data['sequence']
        logger.info(f"FASTA序列长度: {len(fasta_sequence)}")
        
        # 检查预测器
        if predictor is None:
            logger.error("预测器未初始化")
            return jsonify({
                'success': False,
                'error': '预测器未初始化'
            }), 500
        
        # 调用预测
        results = predictor.predict_from_string(fasta_sequence)
        logger.info(f"预测完成，结果数量: {len(results)}")
        
        return jsonify({
            'success': True,
            'results': results
        })
        
    except Exception as e:
        logger.error(f"预测错误: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/labels', methods=['GET'])
def get_labels():
    """
    获取所有可能的亚细胞定位标签
    """
    labels = ['Exosome','Nucleus','Nucleoplasm','Chromatin','Nucleolus','Cytosol','Membrane','Ribosome','Cytoplasm']
    return jsonify({
        'success': True,
        'labels': labels
    })

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        'success': False,
        'error': '接口不存在'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        'success': False,
        'error': '服务器内部错误'
    }), 500

if __name__ == '__main__':
    try:
        # 初始化预测器
        logger.info("正在初始化预测器...")
        initialize_predictor()
        
        # 启动Flask应用
        logger.info("启动Flask API服务器...")
        app.run(
            host='0.0.0.0',  # 允许外部访问
            port=5000,       # 端口号
            debug=False      # 生产环境关闭debug模式
        )
        
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}")
        exit(1)
