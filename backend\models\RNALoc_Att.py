# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# Created by: jas<PERSON><PERSON>
# Created on: 2022-9-22
# Email: <EMAIL>
#
# Copyright © 2022 - CPSS Group
# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
import torch
import torch.nn as nn


from .factory import register_model,create_backbone
from lib.utils import get_loss_fn
from .utils import *
import numpy as np

__all__ = ['RNALoc']


class RNALoc_Att(nn.Module):
    def __init__(self, backbone, feat_dim, cfg, device=None):
        super().__init__()
        self.cfg = cfg
        self.backbone = backbone

        # 智能设备选择：优先使用传入的device，然后检查强制CPU，最后自动检测
        if device is not None:
            self.device = device
        elif getattr(cfg, 'force_cpu', False):
            self.device = torch.device('cpu')
        else:
            # 使用具体的GPU设备号，避免cuda vs cuda:0的不匹配
            if torch.cuda.is_available():
                self.device = torch.device('cuda:0')
            else:
                self.device = torch.device('cpu')

        print(f"RNALoc_Att 使用设备: {self.device}")

        # 创建blocks
        self.blocks = nn.ModuleList([Block(dim=feat_dim, num_heads=cfg.num_heads) for _ in range(cfg.depth)])

        # 根据embed_type创建text_feats，确保在正确的设备上
        if cfg.embed_type == 'bert':
            self.text_feats = torch.tensor(np.load(cfg.embed_path), dtype=torch.float32).to(self.device)
        elif cfg.embed_type == 'onehot':
            self.text_feats = torch.eye(cfg.num_classes).to(self.device)
        else:
            self.text_feats = torch.rand(cfg.num_classes, feat_dim).to(self.device)
        self.depth = cfg.depth
        self.text_head_ = Head(feat_dim, cfg.num_classes)
        #self.text_linear = nn.Linear(cfg.num_classes, feat_dim, bias=False)
        #self.mlp = nn.Linear(768, 9)
        self.attention = LowRankBilinearAttention(feat_dim, feat_dim, feat_dim)

        self.criterion = get_loss_fn(cfg)

        self.net1 = nn.Sequential(
            nn.Conv1d(feat_dim, feat_dim, 1, bias=False),
            nn.BatchNorm1d(feat_dim),
            nn.ReLU(inplace=True)
        )
        self.net2 = nn.Sequential(
            nn.Conv1d(feat_dim, feat_dim, 1, bias=False),
            nn.BatchNorm1d(feat_dim),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x, att_mask,token_type_ids,y=None):
        seqfeat, att_token = self.backbone(x,att_mask,token_type_ids) # [8,token,768]

        # cls [8,768]
        tfeat = torch.stack([self.text_feats for _ in range(x.shape[0])], dim=0)
        if self.cfg.embed_type == 'onehot':
            tfeat = self.text_linear(tfeat).to(self.device)
        for i in range(self.depth):
            tfeat, attn = self.blocks[i](seqfeat, tfeat)
            # tfeat [8,9,768]

        _alpha = self.attention(seqfeat, tfeat) # [8,9,token]
        f = self.net1(seqfeat.transpose(1, 2)).transpose(1, 2)
        _x = torch.bmm(_alpha, f)
        _x = self.net2(_x.transpose(1, 2)).transpose(1, 2) # [8,9,768]

        logits = self.text_head_(_x)
        loss = 0
        if self.training:
            loss = self.criterion(logits, y)

        return {
            'logits': logits,
            'attn_label': attn,  # [B,H,9,9]
            'loss': loss,
            'alpha': _alpha, # [B,9,token]
            'att_token':att_token,
        }


class RNALoc_Att2(nn.Module):
    def __init__(self, backbone, feat_dim, cfg):
        super().__init__()
        self.cfg = cfg
        self.backbone = backbone

        self.text_feats = torch.tensor(np.load(cfg.embed_path), dtype=torch.float32).cuda()
        self.blocks = nn.ModuleList([Block(dim=feat_dim, num_heads=cfg.num_heads) for _ in range(cfg.depth)])

        self.depth = cfg.depth
        self.text_head_ = Head(feat_dim, cfg.num_classes)
        self.classf = nn.Linear(feat_dim, cfg.num_classes)

        self.attention = LowRankBilinearAttention(feat_dim, feat_dim, feat_dim)

        self.criterion = get_loss_fn(cfg)

        self.net1 = nn.Sequential(
            nn.Conv1d(feat_dim, feat_dim, 1, bias=False),
            nn.BatchNorm1d(feat_dim),
            nn.ReLU(inplace=True)
        )
        self.net2 = nn.Sequential(
            nn.Conv1d(feat_dim, feat_dim, 1, bias=False),
            nn.BatchNorm1d(feat_dim),
            nn.ReLU(inplace=True)
        )

    def forward(self, x, att_mask, y=None):
        seqfeat,cls = self.backbone(x, att_mask)  # [8,token,768]
        logits2 = self.classf(cls)
        # cls [8,768]

        tfeat = torch.stack([self.text_feats for _ in range(x.shape[0])], dim=0)

        for i in range(self.depth):
            tfeat, attn = self.blocks[i](seqfeat, tfeat)
            # tfeat [8,9,768]

        _alpha = self.attention(seqfeat, tfeat)  # [8,9,token]
        f = self.net1(seqfeat.transpose(1, 2)).transpose(1, 2)
        _x = torch.bmm(_alpha, f)
        _x = self.net2(_x.transpose(1, 2)).transpose(1, 2)  # [8,9,768]

        logits = self.text_head_(_x)
        # 定义一个池化层来将维度从 1071 缩小到 9
        # pooling = nn.AdaptiveAvgPool1d(9)

        # 对第二个维度进行池化
        # seqfeat_ = pooling(seqfeat.permute(0, 2, 1)).permute(0, 2, 1)
        # logits = self.text_head_(tfeat)
        loss = 0
        if self.training:
            loss = self.criterion(logits2, y)

        return {
            'logits': logits2,
            'attn_label': attn,  # [B,H,9,9]
            'loss': loss,
            'alpha': _alpha,  # [B,9,token]
        }


class RNALoc_Att3(nn.Module):
    def __init__(self, backbone, feat_dim, cfg):
        super().__init__()
        self.cfg = cfg
        self.backbone = backbone

        self.text_feats = torch.tensor(np.load(cfg.embed_path), dtype=torch.float32).cuda()
        #self.blocks = nn.ModuleList([Block(dim=feat_dim, num_heads=cfg.num_heads) for _ in range(cfg.depth)])

        self.depth = cfg.depth
        self.text_head_ = Head(feat_dim, cfg.num_classes)
        self.classf = nn.Linear(feat_dim, cfg.num_classes)
        self.attention = LowRankBilinearAttention(feat_dim, feat_dim, feat_dim)

        self.criterion = get_loss_fn(cfg)

        self.net1 = nn.Sequential(
            nn.Conv1d(feat_dim, feat_dim, 1, bias=False),
            nn.BatchNorm1d(feat_dim),
            nn.ReLU(inplace=True)
        )
        self.net2 = nn.Sequential(
            nn.Conv1d(feat_dim, feat_dim, 1, bias=False),
            nn.BatchNorm1d(feat_dim),
            nn.ReLU(inplace=True)
        )

    def forward(self, x, att_mask, y=None):
        seqfeat,cls = self.backbone(x, att_mask)  # [8,token,768]
        logits2 = self.classf(cls)
        # cls [8,768]

        # tfeat = torch.stack([self.text_feats for _ in range(x.shape[0])], dim=0)
        #
        # for i in range(self.depth):
        #     tfeat, attn = self.blocks[i](seqfeat, tfeat)
        #     # tfeat [8,9,768]
        #
        # _alpha = self.attention(seqfeat, tfeat)  # [8,9,token]
        # f = self.net1(seqfeat.transpose(1, 2)).transpose(1, 2)
        # _x = torch.bmm(_alpha, f)
        # _x = self.net2(_x.transpose(1, 2)).transpose(1, 2)  # [8,9,768]
        #
        # logits = self.text_head_(_x)
        # 定义一个池化层来将维度从 1071 缩小到 9
        # pooling = nn.AdaptiveAvgPool1d(9)

        # 对第二个维度进行池化
        # seqfeat_ = pooling(seqfeat.permute(0, 2, 1)).permute(0, 2, 1)
        # logits = self.text_head_(tfeat)
        loss = 0
        if self.training:
            loss = self.criterion(logits2, y)

        return {
            'logits': logits2,
            #'attn_label': attn,  # [B,H,9,9]
            'loss': loss,
            #'alpha': _alpha,  # [B,9,token]
        }


class RNALoc_Att4(nn.Module):
    def __init__(self, backbone, feat_dim, cfg):
        super().__init__()
        self.cfg = cfg
        self.backbone = backbone

        self.text_feats = torch.tensor(np.load(cfg.embed_path), dtype=torch.float32).cuda()
        #self.blocks = nn.ModuleList([Block(dim=feat_dim, num_heads=cfg.num_heads) for _ in range(cfg.depth)])
        self.classf = nn.Linear(feat_dim, cfg.num_classes)
        self.depth = cfg.depth
        self.text_head_ = Head(feat_dim, cfg.num_classes)

        self.attention = LowRankBilinearAttention(feat_dim, feat_dim, feat_dim)

        self.criterion = get_loss_fn(cfg)

        self.net1 = nn.Sequential(
            nn.Conv1d(feat_dim, feat_dim, 1, bias=False),
            nn.BatchNorm1d(feat_dim),
            nn.ReLU(inplace=True)
        )
        self.net2 = nn.Sequential(
            nn.Conv1d(feat_dim, feat_dim, 1, bias=False),
            nn.BatchNorm1d(feat_dim),
            nn.ReLU(inplace=True)
        )

    def forward(self, x, att_mask, y=None):
        seqfeat,cls = self.backbone(x, att_mask)  # [8,token,768]
        logits2 = self.classf(cls)
        # cls [8,768]

        # tfeat = torch.stack([self.text_feats for _ in range(x.shape[0])], dim=0)
        #
        # for i in range(self.depth):
        #     tfeat, attn = self.blocks[i](seqfeat, tfeat)
        #     # tfeat [8,9,768]
        #
        # _alpha = self.attention(seqfeat, tfeat)  # [8,9,token]
        # f = self.net1(seqfeat.transpose(1, 2)).transpose(1, 2)
        # _x = torch.bmm(_alpha, f)
        # _x = self.net2(_x.transpose(1, 2)).transpose(1, 2)  # [8,9,768]
        #
        # logits = self.text_head_(_x)
        # 定义一个池化层来将维度从 1071 缩小到 9
        # pooling = nn.AdaptiveAvgPool1d(9)

        # 对第二个维度进行池化
        # seqfeat_ = pooling(seqfeat.permute(0, 2, 1)).permute(0, 2, 1)
        # logits = self.text_head_(tfeat)
        loss = 0
        if self.training:
            loss = self.criterion(logits2, y)

        return {
            'logits': logits2,
            #'attn_label': attn,  # [B,H,9,9]
            'loss': loss,
            #'alpha': _alpha,  # [B,9,token]
        }
@register_model
def RNALoc(cfg):
    backbone, feat_dim = create_backbone(cfg.arch)

    # 确定设备
    if getattr(cfg, 'force_cpu', False):
        device = torch.device('cpu')
    else:
        # 使用具体的GPU设备号，避免cuda vs cuda:0的不匹配
        if torch.cuda.is_available():
            device = torch.device('cuda:0')
        else:
            device = torch.device('cpu')

    model = RNALoc_Att(backbone, feat_dim, cfg, device=device)
    return model

@register_model
def RNALocExplainer(cfg):
    backbone, feat_dim = create_backbone(cfg.arch)
    model = RNALoc_Att(backbone, feat_dim, cfg,'cpu')
    return model


