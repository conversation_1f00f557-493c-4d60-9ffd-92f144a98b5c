{"_name_or_path": "zhihan1996/DNABERT-2-117M", "alibi_starting_size": 512, "attention_probs_dropout_prob": 0.0, "auto_map": {"AutoConfig": "configuration_bert.BertConfig", "AutoModel": "bert_layers.BertModel", "AutoModelForMaskedLM": "bert_layers.BertForMaskedLM", "AutoModelForSequenceClassification": "bert_layers.BertForSequenceClassification"}, "classifier_dropout": null, "gradient_checkpointing": false, "hidden_act": "gelu", "hidden_dropout_prob": 0.1, "hidden_size": 768, "initializer_range": 0.02, "intermediate_size": 3072, "layer_norm_eps": 1e-12, "max_position_embeddings": 512, "num_attention_heads": 12, "num_hidden_layers": 12, "position_embedding_type": "absolute", "torch_dtype": "float32", "transformers_version": "4.28.0", "type_vocab_size": 2, "use_cache": true, "vocab_size": 4096}