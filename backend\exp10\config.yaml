model: RNALoc
arch: seq_encoder
data: mRNA
fold_num: 1
mode: train_test
seed: 2024
lr: 1e-05
embed_type: bert
optimizer: AdamW
lr_scheduler: ReduceLROnPlateau
weight_decay: 0.0001
amp: True
num_heads: 8
lamda: 1.0
depth: 6
loss: fdl
gamma_pos: 0.0
gamma_neg: 2.0
tau: 1.0
ema_decay: 0.9997
pct_start: 0.2
batch_size: 8
max_epochs: 80
warmup_epoch: 4
threshold: 0.5
pretrained_model: None
train_path: data/mRNA/train.txt
test_path: data/mRNA/test.txt
label_path: data/mRNA/label.txt
embed_path: data/mRNA/bert.npy
num_classes: 9
exp_dir: experiments/mlic_mRNA/exp1
log_path: experiments/mlic_mRNA/exp1/train.log
ckpt_dir: experiments/mlic_mRNA/exp1/checkpoints
ckpt_best_path: experiments/mlic_mRNA/exp1/checkpoints/best_model.pth
ckpt_ema_best_path: experiments/mlic_mRNA/exp1/checkpoints/best_ema_model.pth
