# mLATTICE mRNA亚细胞定位预测项目 - 前端Dockerfile
# 多阶段构建：构建阶段 + 运行阶段

# 第一阶段：构建Vue应用
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 设置环境变量（构建时使用相对路径）
ENV VITE_API_BASE_URL=/api

# 构建生产版本
RUN npm run build

# 第二阶段：Nginx运行环境
FROM nginx:alpine

# 安装curl用于健康检查
RUN apk add --no-cache curl

# 删除默认的nginx配置
RUN rm /etc/nginx/conf.d/default.conf

# 复制自定义nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 从构建阶段复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
