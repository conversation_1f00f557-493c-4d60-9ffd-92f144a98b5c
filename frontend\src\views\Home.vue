<template>
  <div class="min-h-screen">
    <!-- Hero Section -->
    <section class="section-padding bg-gradient-to-br from-primary-50 to-secondary-50">
      <div class="container-custom">
        <div class="text-center max-w-4xl mx-auto">
          <h1
            class="text-5xl md:text-6xl font-bold gradient-text mb-6 animate-fade-in-up"
            v-motion-slide-visible-once-bottom
          >
            mLATTICE
          </h1>
          <p
            class="text-xl md:text-2xl text-neutral-600 mb-8 leading-relaxed animate-fade-in-up"
            v-motion-slide-visible-once-bottom
            :delay="200"
          >
            Advanced mRNA Subcellular Localization Prediction Platform
          </p>
          <div
            class="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up"
            v-motion-slide-visible-once-bottom
            :delay="400"
          >
            <router-link to="/web-server" class="btn-primary">
              <ServerIcon class="h-5 w-5 mr-2" />
              Start Prediction
            </router-link>
            <router-link to="/download" class="btn-secondary">
              <ArrowDownTrayIcon class="h-5 w-5 mr-2" />
              Download Data
            </router-link>
          </div>
        </div>
      </div>
    </section>

    <!-- Introduction Section -->
    <section class="section-padding bg-white">
      <div class="container-custom max-w-6xl">
        <!-- Introduction Content -->
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-neutral-900 mb-8">
            Introduction
          </h2>
          <div class="prose prose-lg text-neutral-600 space-y-6 max-w-none mx-auto">
            <p class="text-justify leading-relaxed">
              Accurate identification of messenger RNA (mRNA) subcellular localization is pivotal for elucidating its function in cellular processes and enhancing our understanding of its mechanisms in disease development and treatment. Currently, various methods, including single-label and multi-label predictors, have been developed for mRNA subcellular localization prediction. However, multi-label predictors are constrained in effectively learning label-specific features due to their inability to adequately model the intricate relationships between sequences and labels.
            </p>
            <p class="text-justify leading-relaxed">
              Moreover, small-scale and imbalanced datasets further impede the extraction of robust features, often resulting in unsatisfactory performance. To address these issues, we proposed a method for accurately identifying mRNA multiple subcellular localizations, named mLATTICE. mLATTICE leveraged the pre-trained language model DNABERT2 to extract mRNA sequence features and utilized BERT to capture rich label semantic relations.
            </p>
            <p class="text-justify leading-relaxed">
              Additionally, we designed a label alignment module to achieve dynamic interaction between label semantics relations and sequence features, extracting high-quality label-specific features, thereby significantly enhancing performance. Besides, it utilized low-rank bilinear attention to generate high-level feature representation. Moreover, a multi-label focal dice loss function was adopted to mitigate the negative impact of imbalanced data on model performance.
            </p>
            <p class="text-justify leading-relaxed">
              Extensive experiments on an independent test set demonstrated that mLATTICE surpasses state-of-the-art methods in the majority of evaluation metrics. Additionally, an interpretable analysis was conducted, leading to the discovery of several crucial motif bound by specific RNA-binding proteins.
            </p>
          </div>

          <!-- Key Features Tags -->
          <div class="flex flex-wrap justify-center gap-4 mt-10">
            <div class="bg-primary-100 text-primary-800 px-6 py-3 rounded-full font-medium text-sm border border-primary-200">
              Multi-label Predictors
            </div>
            <div class="bg-secondary-100 text-secondary-800 px-6 py-3 rounded-full font-medium text-sm border border-secondary-200">
              DNABERT2
            </div>
            <div class="bg-green-100 text-green-800 px-6 py-3 rounded-full font-medium text-sm border border-green-200">
              Label-specific Features
            </div>
          </div>
        </div>

        <!-- Model Framework -->
        <div class="text-center">
          <h3 class="text-2xl md:text-3xl font-bold text-neutral-900 mb-8">
            Model Framework
          </h3>
          <div class="max-w-4xl mx-auto">
            <div class="card p-6 bg-gradient-to-br from-neutral-50 to-neutral-100 border-2 border-neutral-200">
              <!-- Framework Image Container -->
              <div
                class="relative bg-gradient-to-br from-primary-50 to-secondary-50 rounded-lg p-2 mb-3 group cursor-pointer"
                @click="openImageModal"
                v-show="!imageError"
              >
                <!-- Framework Image -->
                <img
                  src="/images/framework-diagram.png"
                  alt="Model Framework Diagram"
                  class="w-full h-auto object-contain rounded transition-transform group-hover:scale-[1.01]"
                  @error="handleImageError"
                />

                <!-- Click overlay hint -->
                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded transition-all duration-200 flex items-center justify-center">
                  <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white bg-opacity-90 px-3 py-1 rounded-full text-sm text-neutral-700">
                    🔍 Click to enlarge
                  </div>
                </div>
              </div>

              <!-- Fallback when image is not available -->
              <div v-show="imageError" class="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-lg p-2 mb-3">
                <div class="text-center py-16">
                  <ChartBarIcon class="h-16 w-16 text-primary-600 mx-auto mb-4" />
                  <h3 class="text-lg font-semibold text-neutral-700 mb-2">
                    Model Framework
                  </h3>
                  <p class="text-neutral-500 text-sm">
                    Framework diagram will be displayed here
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Image Modal -->
    <div
      v-if="showModal"
      class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
      @click="closeImageModal"
    >
      <div class="relative max-w-7xl max-h-full">
        <img
          src="/images/framework-diagram.png"
          alt="Model Framework Diagram - Full Size"
          class="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
        />
        <button
          @click="closeImageModal"
          class="absolute top-4 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-full p-2 transition-all"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import {
  ServerIcon,
  ArrowDownTrayIcon,
  ChartBarIcon
} from '@heroicons/vue/24/outline'

// Image error handling
const imageError = ref(false)
const showModal = ref(false)

const handleImageError = () => {
  imageError.value = true
}

const openImageModal = () => {
  showModal.value = true
}

const closeImageModal = () => {
  showModal.value = false
}
</script>
