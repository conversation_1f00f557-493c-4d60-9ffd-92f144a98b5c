# RNA亚细胞定位预测项目 - 后端Dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV FORCE_CPU=true
ENV CUDA_VISIBLE_DEVICES=""

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制所有项目文件
COPY . .

# 暴露端口
EXPOSE 5000

# 启动命令 - 使用CPU安全模式
CMD ["python", "start_server_cpu_safe.py"]
