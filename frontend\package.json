{"name": "mlattice-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/vue": "^2.0.18", "@vueuse/motion": "^2.0.0", "pinia": "^2.1.6", "vue": "^3.4.29", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.5", "vite": "^5.3.1", "tailwindcss": "^3.3.3", "postcss": "^8.4.27", "autoprefixer": "^10.4.14"}}