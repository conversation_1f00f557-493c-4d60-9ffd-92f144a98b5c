import os
import yaml
from argparse import Namespace
from pipeline import Pipeline

try:
    # 获取当前文件目录
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    
    # 加载配置文件
    exp_dir = os.path.join(BASE_DIR, 'exp10')
    cfg_path = os.path.join(exp_dir, 'config.yaml')
    
    print(f"配置文件路径: {cfg_path}")
    print(f"配置文件存在: {os.path.exists(cfg_path)}")
    
    if os.path.exists(cfg_path):
        cfg = yaml.load(open(cfg_path, 'r'), Loader=yaml.FullLoader)
        cfg = Namespace(**cfg)
        
        # 设置模型路径
        cfg.ckpt_best_path = os.path.join(exp_dir, 'checkpoints', 'best_model.pth')
        print(f"模型文件路径: {cfg.ckpt_best_path}")
        print(f"模型文件存在: {os.path.exists(cfg.ckpt_best_path)}")
        
        # 创建预测器实例
        predictor = Pipeline(cfg)
        print("预测器初始化成功")
        
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()