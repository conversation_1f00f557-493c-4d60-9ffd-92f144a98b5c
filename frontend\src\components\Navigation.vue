<template>
  <nav class="fixed top-0 left-0 right-0 z-50 glass-effect border-b border-neutral-200/50">
    <div class="container-custom">
      <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
        <!-- Logo -->
        <div class="flex items-center">
          <router-link 
            to="/" 
            class="flex items-center space-x-2 text-xl font-bold gradient-text hover:scale-105 transition-transform duration-200"
          >
            <img :src="RnaIcon" class="h-8 w-8 rna-icon" alt="RNA Icon" />
            <span>mLATTICE</span>
          </router-link>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:block">
          <div class="flex items-center space-x-8">
            <router-link
              v-for="item in navigationItems"
              :key="item.name"
              :to="item.path"
              class="nav-link"
              :class="{ 'active': $route.path === item.path }"
            >
              <component :is="item.icon" class="h-4 w-4 inline mr-2" />
              {{ item.name }}
            </router-link>
          </div>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button
            @click="toggleMobileMenu"
            class="p-2 rounded-apple text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 transition-colors duration-200"
          >
            <Bars3Icon v-if="!isMobileMenuOpen" class="h-6 w-6" />
            <XMarkIcon v-else class="h-6 w-6" />
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <transition
        enter-active-class="transition duration-200 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition duration-75 ease-in"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <div v-if="isMobileMenuOpen" class="md:hidden border-t border-neutral-200/50">
          <div class="px-2 pt-2 pb-3 space-y-1 bg-white/95 backdrop-blur-lg">
            <router-link
              v-for="item in navigationItems"
              :key="item.name"
              :to="item.path"
              @click="closeMobileMenu"
              class="flex items-center px-3 py-2 rounded-apple text-base font-medium text-neutral-700 hover:text-primary-600 hover:bg-neutral-50 transition-colors duration-200"
              :class="{ 'text-primary-600 bg-primary-50': $route.path === item.path }"
            >
              <component :is="item.icon" class="h-5 w-5 mr-3" />
              {{ item.name }}
            </router-link>
          </div>
        </div>
      </transition>
    </div>
  </nav>
</template>

<script setup>
import { ref } from 'vue'
import {
  HomeIcon,
  ServerIcon,
  ArrowDownTrayIcon,
  QuestionMarkCircleIcon,
  EnvelopeIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/vue/24/outline'
import RnaIcon from '@/assets/icons/rna-icon2.svg?url'

const isMobileMenuOpen = ref(false)

const navigationItems = [
  { name: 'Home', path: '/', icon: HomeIcon },
  { name: 'Prediction', path: '/web-server', icon: ServerIcon },
  { name: 'Download', path: '/download', icon: ArrowDownTrayIcon },
  { name: 'Help', path: '/help', icon: QuestionMarkCircleIcon },
  { name: 'Contact', path: '/contact', icon: EnvelopeIcon }
]

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}
</script>

<style scoped>
.rna-icon {
  /* 将图标颜色转换为主题蓝色 #0284c7 */
  filter: brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(1247%) hue-rotate(186deg) brightness(95%) contrast(101%);
  transition: filter 0.2s ease;
}

.rna-icon:hover {
  /* 悬停时稍微变亮 */
  filter: brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(1247%) hue-rotate(186deg) brightness(110%) contrast(101%);
}
</style>
