#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试mRNA亚细胞定位预测API的脚本
"""

import requests
import json
import time

# API服务器地址
BASE_URL = "http://localhost:5000"

# 测试用的FASTA序列
TEST_SEQUENCE = """>NM_014041
GCCGCCATCGCTCTCCCGGGCTTAGAAGGCCCGGCTACTGACGCGCAGTGCCAGACCTTACCCCTCACGGTCCTTAAGTCTCGGTCGCCCTCGCCTCGCAGCCTGCCACCCGCGCTCAGCTGCCCGCCTCCTCAGCCAGCCATGCTGGAGCATCTGAGCTCGCTGCCCACGCAGATGGATTACAAGGGCCAGAAGCTAGCTGAACAGATGTTTCAGGGAATTATTCTTTTTTCTGCAATAGTTGGATTTATCTACGGGTACGTGGCTGAACAGTTCGGGTGGACTGTCTATATAGTTATGGCCGGATTTGCTTTTTCATGTTTGCTGACACTTCCTCCATGGCCCATCTATCGCCGGCATCCTCTCAAGTGGTTACCTGTTCAAGAATCAAGCACAGACGACAAGAAACCAGGGGAAAGAAAAATTAAGAGGCATGCTAAAAATAATTGAGGTTTTCATGATTCAGCACCTGCTTTTGTTTCTGTGAGATGAGCTAAATTGCTTTCATACCCCAGATAAGAGCTAAAACCACCTAATGCTCTTATGGCACAGCTGTGTATAGATTTAGTTCTCTTTATACTTCATTTCTAGCCCAGTTGGGTTTTGATTTATATAAGTAGTTTAGACCTTCTCTTCATAATCTTGCTCTGAGATGGGGAACAGAACACACAAGTATGAAGTTTCTTTCAGGTGTAAATAATGAAAAATAAATGCCTCATAAATGATAGTACAATGTAACTATCAAAGTTTTATAATTCATTATGAGTTAACCATTTTAATGTTTCCAATTAAACCTCATAGTGCAAGTTCTTTGTCTAAA"""

def test_health_check():
    """测试健康检查接口"""
    print("=" * 60)
    print("测试健康检查接口...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return False

def test_labels_endpoint():
    """测试标签接口"""
    print("=" * 60)
    print("测试标签接口...")
    try:
        response = requests.get(f"{BASE_URL}/labels", timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return False

def test_prediction():
    """测试预测接口"""
    print("=" * 60)
    print("测试预测接口...")
    
    # 准备请求数据
    data = {
        "sequence": TEST_SEQUENCE
    }
    
    try:
        print("发送预测请求...")
        start_time = time.time()
        
        response = requests.post(
            f"{BASE_URL}/predict",
            json=data,
            headers={'Content-Type': 'application/json'},
            timeout=120  # 预测可能需要较长时间
        )
        
        end_time = time.time()
        print(f"预测耗时: {end_time - start_time:.2f} 秒")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("预测成功！")
            print(f"成功状态: {result.get('success')}")
            print(f"消息: {result.get('message')}")
            
            if 'results' in result and result['results']:
                for i, seq_result in enumerate(result['results']):
                    print(f"\n序列 {i+1} 结果:")
                    print(f"  序列ID: {seq_result.get('sequence_id')}")
                    print(f"  序列长度: {seq_result.get('sequence_length')}")
                    print(f"  预测位置: {seq_result.get('predicted_locations')}")
                    
                    print("  所有位置分数:")
                    for location, score in seq_result.get('scores', {}).items():
                        prediction = seq_result.get('predictions', {}).get(location, 0)
                        status = "阳性" if prediction == 1 else "阴性"
                        print(f"    {location}: {score:.6f} ({status})")
            
            return True
        else:
            print(f"预测失败: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试mRNA亚细胞定位预测API")
    print("确保后端服务器正在运行在 http://localhost:5000")
    
    # 测试健康检查
    if not test_health_check():
        print("❌ 健康检查失败，请确保服务器正在运行")
        return
    
    # 测试标签接口
    if not test_labels_endpoint():
        print("❌ 标签接口测试失败")
        return
    
    # 测试预测接口
    if not test_prediction():
        print("❌ 预测接口测试失败")
        return
    
    print("=" * 60)
    print("✅ 所有API测试通过！")
    print("后端服务器工作正常，可以启动前端进行完整测试。")

if __name__ == "__main__":
    main()
