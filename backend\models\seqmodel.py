#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time : 2022/7/26 21:04
# <AUTHOR> fhh
# @FileName: model.py
# @Software: PyCharm
import numpy as np
import torch
import torch.nn as nn
from transformers import AutoModel
import logging
import os

logger = logging.getLogger(__name__)
from .factory import register_backbone

__all__ = ['BERTClass','RNAclass']
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
path = os.path.join(BASE_DIR, 'DNAbert2_attention')
# path = '/home/<USER>/Code/CoCo-main/DNAbert2_attention'
class BERTClass(nn.Module):
    def __init__(self, dp1, dp2):
        super(BERTClass, self).__init__()
        self.bert_model = AutoModel.from_pretrained(path,trust_remote_code=True, output_attentions=True)
        self.sequential = torch.nn.Sequential(torch.nn.Dropout(dp1),
                                              torch.nn.Linear(768, 1024),
                                              torch.nn.Dropout(dp2),
                                              torch.nn.Linear(1024, 768)
                                              )

    def forward(self, input_ids, attn_mask, token_type_ids):
        out,cls,attn_weight,attention_prob = self.bert_model(input_ids, attention_mask=attn_mask, token_type_ids=token_type_ids)

        hidden_states = torch.stack(list(out), dim=0)

        hidden_states = self.sequential(hidden_states)

        return hidden_states,attention_prob[-1] # last layer attention [8, 12, 1059, 1059]


@register_backbone(feat_dim=768)
def seq_encoder(**kwargs):
    return BERTClass(dp1=0.1, dp2=0.1)

from multimolecule import RnaBertModel
# path2 = os.path.join(BASE_DIR, 'models/pretrained/rnabert')
class RNAclass(nn.Module):
    def __init__(self, dp1, dp2):
        super(RNAclass, self).__init__()
        self.bert_model = RnaBertModel.from_pretrained(path2)
        self.sequential = torch.nn.Sequential(torch.nn.Dropout(dp1),
                                              torch.nn.Linear(120, 256),
                                              torch.nn.Dropout(dp2),
                                              torch.nn.Linear(256, 768)
                                              )

    def forward(self, input_ids, attn_mask):
        out = self.bert_model(input_ids,attn_mask).last_hidden_state
        cls = self.bert_model(input_ids, attn_mask).pooler_output
        cls  = self.sequential(cls)
        hidden_states = torch.stack(list(out), dim=0)
        hidden_states = self.sequential(hidden_states)
        return hidden_states,cls

@register_backbone(feat_dim=768)
def RNA_encoder(**kwargs):
    return RNAclass(dp1=0.1, dp2=0.1)


from multimolecule import RnaFmModel
# path3 = os.path.join(BASE_DIR, 'models/pretrained/mRNA-FM')
class RNAFM(nn.Module):
    def __init__(self, dp1, dp2):
        super(RNAFM, self).__init__()
        self.bert_model = RnaFmModel.from_pretrained(path3)
        self.sequential = torch.nn.Sequential(torch.nn.Dropout(dp1),
                                              torch.nn.Linear(1280, 768),
                                              torch.nn.Dropout(dp2),
                                              torch.nn.Linear(768, 768)
                                              )

    def forward(self, input_ids, attn_mask):
        out = self.bert_model(input_ids,attn_mask).last_hidden_state
        cls = self.bert_model(input_ids, attn_mask).pooler_output
        cls = self.sequential(cls)
        hidden_states = torch.stack(list(out), dim=0)
        hidden_states = self.sequential(hidden_states)

        return hidden_states,cls

@register_backbone(feat_dim=768)
def RNA_FM(**kwargs):
    return RNAFM(dp1=0.1, dp2=0.1)


from multimolecule import SpliceBertModel
# path4 = os.path.join(BASE_DIR, 'models/pretrained/spliceBERT')
class spliceRNA(nn.Module):
    def __init__(self, dp1, dp2):
        super(spliceRNA, self).__init__()
        self.bert_model = SpliceBertModel.from_pretrained(path4)
        self.sequential = torch.nn.Sequential(torch.nn.Dropout(dp1),
                                              torch.nn.Linear(512, 768),
                                              torch.nn.Dropout(dp2),
                                              torch.nn.Linear(768, 768)
                                              )

    def forward(self, input_ids, attn_mask):
        out = self.bert_model(input_ids,attn_mask).last_hidden_state
        cls = self.bert_model(input_ids, attn_mask).pooler_output
        cls = self.sequential(cls)
        hidden_states = torch.stack(list(out), dim=0)
        hidden_states = self.sequential(hidden_states)

        return hidden_states,cls

@register_backbone(feat_dim=768)
def splice_RNA(**kwargs):
    return spliceRNA(dp1=0.1, dp2=0.1)

