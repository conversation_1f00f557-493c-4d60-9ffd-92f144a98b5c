<template>
  <div class="min-h-screen section-padding">
    <div class="container-custom max-w-4xl">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-4xl md:text-5xl font-bold text-neutral-900 mb-4">
          mRNA Sequence Analysis
        </h1>
        <p class="text-xl text-neutral-600">
          Submit your mRNA sequences for subcellular localization prediction
        </p>
      </div>

      <!-- Main Form -->
      <div class="card p-8 mb-8">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Input Method Tabs -->
          <div class="mb-6">
            <div class="flex space-x-1 bg-neutral-100 p-1 rounded-lg">
              <button
                type="button"
                @click="inputMethod = 'manual'"
                :class="[
                  'flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all duration-200',
                  inputMethod === 'manual'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-neutral-600 hover:text-neutral-900'
                ]"
              >
                <PencilIcon class="h-4 w-4 inline mr-2" />
                Manual Input
              </button>
              <button
                type="button"
                @click="inputMethod = 'file'"
                :class="[
                  'flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all duration-200',
                  inputMethod === 'file'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-neutral-600 hover:text-neutral-900'
                ]"
              >
                <DocumentArrowUpIcon class="h-4 w-4 inline mr-2" />
                Upload File
              </button>
            </div>
          </div>

          <!-- Input Content -->
          <div>
            <label class="block text-lg font-medium text-neutral-900 mb-3">
              {{ inputMethod === 'manual' ? 'Enter the mRNA sequences in FASTA format:' : 'Upload FASTA file:' }}
            </label>

            <!-- Manual Input -->
            <div v-if="inputMethod === 'manual'">
              <textarea
                id="rna-sequence"
                v-model="rnaStore.sequence"
                placeholder="Paste your mRNA sequence here in FASTA format..."
                class="textarea-field h-64 font-mono text-sm"
                :class="{ 'border-red-300 focus:ring-red-500': rnaStore.error }"
              ></textarea>
            </div>

            <!-- File Upload -->
            <div v-else class="space-y-4">
              <!-- File Drop Zone -->
              <div
                @drop="handleFileDrop"
                @dragover.prevent
                @dragenter.prevent
                :class="[
                  'border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200',
                  isDragOver ? 'border-primary-400 bg-primary-50' : 'border-neutral-300 hover:border-neutral-400',
                  rnaStore.error ? 'border-red-300' : ''
                ]"
              >
                <DocumentArrowUpIcon class="h-12 w-12 text-neutral-400 mx-auto mb-4" />
                <div class="space-y-2">
                  <p class="text-lg font-medium text-neutral-900">
                    Drop your FASTA file here, or
                    <label for="file-input" class="text-primary-600 hover:text-primary-700 cursor-pointer underline">
                      browse
                    </label>
                  </p>
                  <p class="text-sm text-neutral-500">
                    Supports .fasta, .fa, .fas, .txt files
                  </p>
                  <input
                    id="file-input"
                    type="file"
                    accept=".fasta,.fa,.fas,.txt"
                    @change="handleFileSelect"
                    class="hidden"
                  />
                </div>
              </div>

              <!-- Selected File Info -->
              <div v-if="selectedFile" class="bg-neutral-50 rounded-lg p-4">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <DocumentTextIcon class="h-5 w-5 text-neutral-400 mr-2" />
                    <span class="text-sm font-medium text-neutral-900">{{ selectedFile.name }}</span>
                    <span class="text-sm text-neutral-500 ml-2">({{ formatFileSize(selectedFile.size) }})</span>
                  </div>
                  <button
                    type="button"
                    @click="removeFile"
                    class="text-neutral-400 hover:text-red-500 transition-colors"
                  >
                    <XMarkIcon class="h-4 w-4" />
                  </button>
                </div>
              </div>

              <!-- File Content Preview -->
              <div v-if="fileContent" class="space-y-2">
                <label class="block text-sm font-medium text-neutral-700">File Content Preview:</label>
                <div class="bg-neutral-50 rounded-lg p-4 max-h-32 overflow-y-auto">
                  <pre class="text-xs font-mono text-neutral-600 whitespace-pre-wrap">{{ fileContentPreview }}</pre>
                </div>
              </div>
            </div>

            <!-- Error Message -->
            <div v-if="rnaStore.error" class="mt-2 text-red-600 text-sm flex items-center">
              <ExclamationTriangleIcon class="h-4 w-4 mr-1" />
              {{ rnaStore.error }}
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-4">
            <button
              type="button"
              @click="loadExample"
              class="btn-outline flex items-center justify-center"
            >
              <DocumentDuplicateIcon class="h-5 w-5 mr-2" />
              Example
            </button>
            
            <button
              type="submit"
              :disabled="rnaStore.isLoading || !hasValidInput"
              class="btn-primary flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <template v-if="rnaStore.isLoading">
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Processing...
              </template>
              <template v-else>
                <PaperAirplaneIcon class="h-5 w-5 mr-2" />
                Submit
              </template>
            </button>
            
            <button
              type="button"
              @click="clearSequence"
              class="btn-secondary flex items-center justify-center"
            >
              <TrashIcon class="h-5 w-5 mr-2" />
              Clear
            </button>
          </div>
        </form>
      </div>

      <!-- Instructions -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold text-neutral-900 mb-4 flex items-center">
          <InformationCircleIcon class="h-5 w-5 mr-2 text-primary-600" />
          Submission Guidelines
        </h3>
        <div class="space-y-3 text-neutral-600">
          <div class="flex items-start">
            <span class="font-medium text-neutral-900 mr-2">(1)</span>
            <span class="text-justify">For each submission, the number of mRNA sequences is limited to 100,000 nt or less. Please note that longer sequences may result in slower processing times as our cloud server uses CPU-based inference.</span>
          </div>
          <div class="flex items-start">
            <span class="font-medium text-neutral-900 mr-2">(2)</span>
            <span class="text-justify">Each mRNA sequence must start with a greater-than symbol (">") in the first column. The words right after the ">" symbol in the single initial line are optional and only used for the purpose of identification and description.</span>
          </div>
          <div class="flex items-start">
            <span class="font-medium text-neutral-900 mr-2">(3)</span>
            <span class="text-justify">The query mRNA sequence should be no shorter than 8 characters. The accepted characters are: A, C, G, T. If a query sequence contains any illegal character, the prediction will be stopped.</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useRnaStore } from '../stores/rna'
import {
  DocumentDuplicateIcon,
  PaperAirplaneIcon,
  TrashIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
  PencilIcon,
  DocumentArrowUpIcon,
  DocumentTextIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

const router = useRouter()
const rnaStore = useRnaStore()

// Input method state
const inputMethod = ref('manual')
const selectedFile = ref(null)
const fileContent = ref('')
const isDragOver = ref(false)

// Computed properties
const hasValidInput = computed(() => {
  if (inputMethod.value === 'manual') {
    return rnaStore.sequence.trim()
  } else {
    return fileContent.value.trim()
  }
})

const fileContentPreview = computed(() => {
  if (!fileContent.value) return ''
  const lines = fileContent.value.split('\n')
  if (lines.length <= 10) return fileContent.value
  return lines.slice(0, 10).join('\n') + '\n... (showing first 10 lines)'
})

// Watch for input method changes and clear errors
watch(inputMethod, (newMethod, oldMethod) => {
  // Only clear errors when user manually switches, not when programmatically set
  if (oldMethod !== undefined) {
    rnaStore.clearError()
  }

  if (newMethod === 'manual' && oldMethod !== undefined) {
    // When switching to manual, clear file state
    selectedFile.value = null
    fileContent.value = ''
    const fileInput = document.getElementById('file-input')
    if (fileInput) {
      fileInput.value = ''
    }
  }
})

// File handling functions
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const validateFileContent = (content) => {
  // Use the same validation logic as the store
  if (!content.trim()) {
    return 'File content is empty'
  }

  // Check if content starts with FASTA format
  if (!content.trim().startsWith('>')) {
    return 'Invalid FASTA format. Sequences must start with ">" symbol'
  }

  // Additional validation can be added here
  return null
}

const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    processFile(file)
  }
}

const handleFileDrop = (event) => {
  event.preventDefault()
  isDragOver.value = false

  const files = event.dataTransfer.files
  if (files.length > 0) {
    processFile(files[0])
  }
}

const processFile = (file) => {
  // File size validation removed to allow larger files

  // Validate file type
  const validExtensions = ['.fasta', '.fa', '.fas', '.txt']
  const fileName = file.name.toLowerCase()
  const isValidType = validExtensions.some(ext => fileName.endsWith(ext))

  if (!isValidType) {
    rnaStore.setError('Please upload a valid FASTA file (.fasta, .fa, .fas, .txt)')
    return
  }

  selectedFile.value = file

  // Read file content
  const reader = new FileReader()
  reader.onload = (e) => {
    const content = e.target.result
    const error = validateFileContent(content)

    if (error) {
      rnaStore.setError(error)
      fileContent.value = ''
    } else {
      fileContent.value = content
      rnaStore.clearError()
    }
  }

  reader.onerror = () => {
    rnaStore.setError('Error reading file')
  }

  reader.readAsText(file)
}

const removeFile = () => {
  selectedFile.value = null
  fileContent.value = ''
  rnaStore.clearError()

  // Clear file input
  const fileInput = document.getElementById('file-input')
  if (fileInput) {
    fileInput.value = ''
  }
}

const loadExample = () => {
  console.log('Loading example...')

  // Switch to manual input mode (this will trigger the watch and clear file state)
  inputMethod.value = 'manual'

  // Clear any file-related state manually to ensure it's clean
  selectedFile.value = null
  fileContent.value = ''
  const fileInput = document.getElementById('file-input')
  if (fileInput) {
    fileInput.value = ''
  }

  // Load the example sequence
  rnaStore.loadExample()

  console.log('Example loaded, sequence length:', rnaStore.sequence.length)
}

const clearSequence = () => {
  if (inputMethod.value === 'manual') {
    rnaStore.clearSequence()
  } else {
    removeFile()
  }
}

const handleSubmit = async () => {
  // Set the sequence content based on input method
  if (inputMethod.value === 'file') {
    rnaStore.sequence = fileContent.value
  }

  const success = await rnaStore.submitSequence()
  if (success) {
    router.push('/results')
  }
}
</script>
