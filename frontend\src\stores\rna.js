import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useRnaStore = defineStore('rna', () => {
  // State
  const sequence = ref('')
  const results = ref(null)
  const isLoading = ref(false)
  const error = ref(null)

  // Example RNA sequence
  const exampleSequence = `>NM_014041
GGAGGCCAGAGGTGGGAGGCTGGCGGCCTGCTCGGTCTCAGCAGACCCTCCTAGTCCCTCAGGAGACCTTGCCTTTGCCCCACTTGCTCGTTATCCAGCCTGGGCCATGAAGCAGAGGACAGTTAGGGACCCTGAGCACGCGGTGGTCACCCCGGTGCTCACCCCTCCCTGTGTGTCCGACCTTGGCCCTGCTAAGATCCTGTGTTTTGAATTCTGGCAAGGGTTGGATGAAAGGGCAGGGCTCCAGAAACCAGCTCAGACGTTTGCTTGGGACCTGCATGATGAGTGGGAATCGGAGGGCACCAGCCCTGCTGTCCCAGGCTCAGGCCCCCATCTGCTCCCCAGGTCATGCAGCCTGGGCCCCCATGCCGTGCAGCTCGCACATATGTGGGGCAGAGCAGCCACCCTGCCCCCAGCAGCAGCCGTCCATCGTCAGACGTGATCATTTCCTGAGGCCTCGAGTGTGTCAGGGTGTTTGTGCCTCATAACAACCCACAGGATGGTCACCCCCGCTTTGCAGATGAAGAAACCAAAGCAGTTTTGCCGACGGATGGGGCAAAAGAAGCAGCGACCAGCTAGAGCAGGGCAGCCACACAGCTCGTCCGACGCAGCCCAGGCACCTGCAGAGCAGCCACACAGCTCGTCCGATGCAGCCCAGGCACCTTGCCCCAGGGAGCGCTGCTTGGGACCGCCCACCACTCCGGGCCCATACCGCAGCATCTATTTCTCAAGCCCAAAGGGCCACCTTACCCGACTGGGGTTGGAGTTCTTCGACCAGCCGGCAGTCCCCCTGGCCCGGGCATTTCTGGGACAGGTCCTAGTCCGGCGACTTCCTAATGGCACAGAACTCCGAGGCCGCATCGTGGAGACCGAGGCATACCTGGGGCCAGAGGATGAAGCCGCCCACTCAAGGGGTGGCCGGCAGACCCCCCGCAACCGAGGCATGTTCATGAAGCCGGGGACCCTGTACGTGTACATCATTTACGGCATGTACTTCTGCATGAACATCTCCAGCCAGGGGGACGGGGCTTGCGTCTTGCTGCGAGCACTGGAGCCCCTGGAAGGTCTGGAGACCATGCGTCAGCTTCGCAGCACCCTCCGGAAAGGCACCGCCAGCCGTGTCCTCAAGGACCGCGAGCTCTGCAGTGGCCCCTCCAAGCTGTGCCAGGCCCTGGCCATCAACAAGAGCTTTGACCAGAGGGACCTGGCACAGGATGAAGCTGTATGGCTGGAGCGTGGTCCCCTGGAGCCCAGTGAGCCGGCTGTAGTGGCAGCAGCCCGGGTGGGCGTCGGCCATGCAGGGGAGTGGGCCCGGAAACCCCTCCGCTTCTATGTCCGGGGCAGCCCCTGGGTCAGTGTGGTCGACAGAGTGGCTGAGCAGGACACACAGGCCTGAGCAAAGGGCCTGCCCAGACAAGATTTTTTAATTGTTTAAAAACCGAATAAATGTTTTATTTCTAGAAAA`

  // Actions
  const setSequence = (newSequence) => {
    sequence.value = newSequence
    error.value = null
  }

  const loadExample = () => {
    sequence.value = exampleSequence
    error.value = null
  }

  const clearSequence = () => {
    sequence.value = ''
    error.value = null
  }

  const validateSequence = (seq) => {
    if (!seq.trim()) {
      return 'Please enter an RNA sequence'
    }
    
    if (!seq.startsWith('>')) {
      return 'RNA sequence must start with ">" symbol'
    }
    
    const lines = seq.split('\n').map(line => line.trim()).filter(line => line)
    if (lines.length < 2) {
      return 'Please provide both header and sequence'
    }
    
    // 更精确地提取序列数据：只取不以>开头的行
    let sequenceData = ''
    for (const line of lines) {
      if (!line.startsWith('>')) {
        // 移除所有空白字符，只保留字母
        sequenceData += line.replace(/\s/g, '')
      }
    }
    
    if (sequenceData.length < 8) {
      return 'RNA sequence should be no shorter than 8 characters'
    }
    
    // 检查是否只包含ACGT字符
    const validChars = /^[ACGT]+$/i
    if (!validChars.test(sequenceData)) {
      // 找出非法字符用于调试
      const invalidChars = sequenceData.match(/[^ACGT]/gi)
      console.log('Invalid characters found:', invalidChars)
      return 'RNA sequence should only contain A, C, G, T characters'
    }
    
    if (sequenceData.length > 100000) {
      return 'RNA sequence should not exceed 100,000 nucleotides'
    }
    
    return null
  }

  const submitSequence = async () => {
    // 验证序列格式
    const validationError = validateSequence(sequence.value)
    if (validationError) {
      error.value = validationError
      return false
    }

    isLoading.value = true
    error.value = null

    try {
      // 调用后端API进行预测
      // 使用环境变量或默认值
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000'

      // 创建AbortController用于超时控制
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 600000) // 10分钟超时

      const response = await fetch(`${apiBaseUrl}/predict`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sequence: sequence.value
        }),
        signal: controller.signal
      })

      // 清除超时定时器
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`)
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || '预测失败')
      }

      // 处理API返回的结果
      if (data.results && data.results.length > 0) {
        // 将后端返回的结果转换为前端需要的格式
        const processedResults = data.results.map(result => {
          // 找到预测为1的亚细胞定位
          const predictedLocations = result.predicted_locations || []

          // 检查是否所有预测都是0
          const hasPositivePrediction = Object.values(result.predictions).some(pred => pred === 1)

          return {
            sequenceId: result.sequence_id,
            length: result.sequence_length,
            allPredictions: result.predictions,
            allScores: result.scores,
            predictedLocations: predictedLocations,
            hasPositivePrediction: hasPositivePrediction
          }
        })

        // 如果只有一条序列，直接使用第一个结果
        // 如果有多条序列，存储所有结果
        results.value = processedResults.length === 1 ? processedResults[0] : processedResults
      } else {
        throw new Error('未收到有效的预测结果')
      }

      return true
    } catch (err) {
      console.error('API调用失败:', err)

      // 处理不同类型的错误
      if (err.name === 'AbortError') {
        error.value = '预测超时，请尝试减少序列数量或长度'
      } else {
        error.value = `预测失败: ${err.message}`
      }
      return false
    } finally {
      isLoading.value = false
    }
  }

  const clearResults = () => {
    results.value = null
  }

  const setError = (errorMessage) => {
    error.value = errorMessage
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    sequence,
    results,
    isLoading,
    error,
    exampleSequence,

    // Actions
    setSequence,
    loadExample,
    clearSequence,
    validateSequence,
    submitSequence,
    clearResults,
    setError,
    clearError
  }
})
