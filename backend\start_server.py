#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动mRNA亚细胞定位预测API服务器的脚本
包含环境检查和依赖验证
"""

import os
import sys
import subprocess
import importlib.util

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✓ Python版本检查通过: {sys.version}")
    return True

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = [
        'torch',
        'transformers', 
        'flask',
        'flask_cors',
        'numpy',
        'pandas',
        'yaml',
        'Bio'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'yaml':
                import yaml
            elif package == 'Bio':
                from Bio import SeqIO
            elif package == 'flask_cors':
                from flask_cors import CORS
            else:
                importlib.import_module(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print("pip install -r requirements.txt")
        return False
    
    print("✓ 所有依赖包检查通过")
    return True

def check_model_files():
    """检查模型文件是否存在"""
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 检查配置文件
    config_path = os.path.join(base_dir, 'exp10', 'config.yaml')
    if not os.path.exists(config_path):
        print(f"✗ 配置文件不存在: {config_path}")
        return False
    print(f"✓ 配置文件存在: {config_path}")
    
    # 检查模型权重文件
    model_path = os.path.join(base_dir, 'exp10', 'checkpoints', 'best_model.pth')
    if not os.path.exists(model_path):
        print(f"✗ 模型文件不存在: {model_path}")
        return False
    print(f"✓ 模型文件存在: {model_path}")
    
    # 检查DNAbert2模型文件
    dnabert_path = os.path.join(base_dir, 'DNAbert2_attention')
    if not os.path.exists(dnabert_path):
        print(f"✗ DNAbert2模型目录不存在: {dnabert_path}")
        return False
    print(f"✓ DNAbert2模型目录存在: {dnabert_path}")
    
    # 检查标签文件
    label_path = os.path.join(base_dir, 'data', 'mRNA', 'label.txt')
    if not os.path.exists(label_path):
        print(f"✗ 标签文件不存在: {label_path}")
        return False
    print(f"✓ 标签文件存在: {label_path}")
    
    return True

def check_gpu():
    """检查GPU可用性"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✓ GPU可用: {gpu_name} (共{gpu_count}个GPU)")
            return True
        else:
            print("⚠ GPU不可用，将使用CPU运行（速度较慢）")
            return True
    except ImportError:
        print("✗ 无法检查GPU状态（torch未安装）")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("mRNA亚细胞定位预测API服务器启动检查")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    print("\n" + "-" * 40)
    print("检查依赖包...")
    if not check_dependencies():
        sys.exit(1)
    
    print("\n" + "-" * 40)
    print("检查模型文件...")
    if not check_model_files():
        sys.exit(1)
    
    print("\n" + "-" * 40)
    print("检查GPU状态...")
    check_gpu()
    
    print("\n" + "=" * 60)
    print("所有检查通过！正在启动API服务器...")
    print("=" * 60)
    
    # 启动Flask应用
    try:
        from app import app, initialize_predictor
        
        print("\n服务器启动信息:")
        print("- 地址: http://localhost:5000")
        print("- 健康检查: http://localhost:5000/health")
        print("- 预测接口: http://localhost:5000/predict")
        print("- 按 Ctrl+C 停止服务器")
        print("\n" + "-" * 60)
        
        # 初始化预测器
        print("正在初始化预测器...")
        initialize_predictor()
        print("预测器初始化完成！")
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False
        )
        
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"\n启动服务器时发生错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
