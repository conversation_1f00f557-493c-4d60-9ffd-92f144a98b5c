#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设备修复的脚本
"""

import os
import sys
import torch

def test_device_detection():
    """测试设备检测"""
    print("=" * 50)
    print("设备检测测试")
    print("=" * 50)
    
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    
    # 测试设备选择
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"选择的设备: {device}")
    
    # 测试强制CPU
    force_cpu_device = torch.device('cpu')
    print(f"强制CPU设备: {force_cpu_device}")
    
    return device

def test_tensor_operations():
    """测试张量操作"""
    print("\n" + "=" * 50)
    print("张量操作测试")
    print("=" * 50)
    
    # 测试CPU
    print("测试CPU操作...")
    cpu_device = torch.device('cpu')
    x_cpu = torch.randn(2, 3).to(cpu_device)
    y_cpu = torch.randn(2, 3).to(cpu_device)
    z_cpu = x_cpu + y_cpu
    print(f"CPU张量设备: {x_cpu.device}")
    print(f"CPU运算结果设备: {z_cpu.device}")
    
    # 测试GPU（如果可用）
    if torch.cuda.is_available():
        print("测试GPU操作...")
        gpu_device = torch.device('cuda')
        x_gpu = torch.randn(2, 3).to(gpu_device)
        y_gpu = torch.randn(2, 3).to(gpu_device)
        z_gpu = x_gpu + y_gpu
        print(f"GPU张量设备: {x_gpu.device}")
        print(f"GPU运算结果设备: {z_gpu.device}")
    
    print("张量操作测试完成!")

if __name__ == "__main__":
    try:
        device = test_device_detection()
        test_tensor_operations()
        print("\n✅ 所有测试通过!")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        sys.exit(1)
