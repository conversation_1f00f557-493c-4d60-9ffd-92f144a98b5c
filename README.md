# mRNA亚细胞定位预测系统

这是一个完整的前后端集成系统，用于预测mRNA的亚细胞定位。系统包含一个Vue.js前端界面和一个基于Flask的后端API服务。

## 系统架构

- **前端**: Vue.js + Tailwind CSS + Vite
- **后端**: Flask + PyTorch + Transformers
- **模型**: 基于DNAbert2的深度学习模型

## 功能特性

- 支持FASTA格式的mRNA序列输入
- 支持单条或多条序列批量预测
- 预测9种亚细胞定位：Exosome, Nucleus, Nucleoplasm, Chromatin, Nucleolus, Cytosol, Membrane, Ribosome, Cytoplasm
- 提供详细的置信度分数
- 结果可视化展示
- 支持结果下载

## 安装和运行

### 后端设置

1. **进入后端目录**
   ```bash
   cd backend
   ```

2. **安装Python依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **启动后端服务器**
   ```bash
   python start_server.py
   ```
   
   或者直接运行：
   ```bash
   python app.py
   ```

   服务器将在 `http://localhost:5000` 启动

### 前端设置

1. **进入前端目录**
   ```bash
   cd frontend
   ```

2. **安装Node.js依赖**
   ```bash
   npm install
   ```

3. **启动前端开发服务器**
   ```bash
   npm run dev
   ```

   前端将在 `http://localhost:5173` 启动

## 使用方法

1. **启动服务**
   - 先启动后端API服务器（端口5000）
   - 再启动前端开发服务器（端口5173）

2. **访问Web界面**
   - 打开浏览器访问 `http://localhost:5173`
   - 点击导航栏中的"Web Server"

3. **输入序列**
   - 在文本框中输入FASTA格式的mRNA序列
   - 可以点击"Example"按钮加载示例序列
   - 点击"Submit"提交预测

4. **查看结果**
   - 系统会自动跳转到结果页面
   - 显示预测的亚细胞定位和置信度分数
   - 可以下载详细的预测结果

## API接口

### 健康检查
```
GET /health
```

### 预测接口
```
POST /predict
Content-Type: application/json

{
  "sequence": "FASTA格式的mRNA序列"
}
```

### 获取标签
```
GET /labels
```

## 输入格式要求

- 序列必须以">"开头（FASTA格式）
- 序列长度不少于8个字符
- 只接受A、C、G、T字符
- 单次提交序列总长度不超过100,000个核苷酸

## 输出格式

系统返回每条序列的预测结果，包括：
- 序列ID和长度
- 预测的亚细胞定位
- 所有位置的置信度分数
- 预测为阳性的所有位置

## 技术细节

### 后端技术栈
- Flask: Web框架
- PyTorch: 深度学习框架
- Transformers: 预训练模型库
- BioPython: 生物信息学工具
- Flask-CORS: 跨域支持

### 前端技术栈
- Vue.js 3: 前端框架
- Pinia: 状态管理
- Vue Router: 路由管理
- Tailwind CSS: 样式框架
- Heroicons: 图标库

### 模型信息
- 基于DNAbert2的Transformer架构
- 支持多标签分类（9个亚细胞定位）
- 使用sigmoid激活函数输出概率
- 阈值0.5进行二分类判断

## 故障排除

### 常见问题

1. **后端启动失败**
   - 检查Python版本（需要3.7+）
   - 确认所有依赖包已安装
   - 检查模型文件是否存在

2. **前端无法连接后端**
   - 确认后端服务器正在运行
   - 检查端口5000是否被占用
   - 确认CORS设置正确

3. **预测速度慢**
   - 建议使用GPU加速
   - 检查序列长度（过长会影响速度）

4. **内存不足**
   - 减少批量序列数量
   - 使用更小的模型或减少序列长度

### 日志查看

后端服务器会输出详细的日志信息，包括：
- 模型加载状态
- 请求处理过程
- 错误信息

## 开发说明

### 项目结构
```
mLATTICE/
├── backend/                 # 后端代码
│   ├── app.py              # Flask应用主文件
│   ├── pipeline.py         # 预测管道
│   ├── start_server.py     # 启动脚本
│   ├── requirements.txt    # Python依赖
│   ├── exp10/              # 模型配置和权重
│   ├── DNAbert2_attention/ # 预训练模型
│   └── models/             # 模型定义
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── stores/         # 状态管理
│   │   └── components/     # 通用组件
│   └── package.json        # Node.js依赖
└── README.md               # 项目说明
```

### 扩展开发

如需添加新功能或修改现有功能，请参考：
- 后端API：修改 `backend/app.py`
- 前端界面：修改 `frontend/src/views/`
- 状态管理：修改 `frontend/src/stores/rna.js`
- 预测逻辑：修改 `backend/pipeline.py`

## 许可证

本项目基于现有的深度学习模型构建，请遵守相关的开源许可证。
