# 🚀 服务器启动指南

本项目支持在GPU和CPU上运行，提供多种启动方式以适应不同的环境需求。

## 📋 启动脚本说明

### 1. **智能启动模式**（推荐）
```bash
python start_server.py
```
**特点：**
- ✅ 完整的环境检查（Python版本、依赖包、模型文件）
- ✅ 自动GPU/CPU检测和选择
- ✅ 详细的启动信息和错误诊断
- ✅ 适合生产环境和首次运行

**适用场景：**
- 生产环境部署
- 首次运行或环境验证
- 需要完整诊断信息时

### 2. **快速启动模式**
```bash
python app.py
```
**特点：**
- ⚡ 快速启动，无环境检查
- ✅ 自动检测GPU/CPU
- ✅ 适合开发和调试

**适用场景：**
- 日常开发调试
- 频繁重启测试
- 确认环境正常后的快速启动

### 3. **安全CPU模式**
```bash
python start_server_cpu_safe.py
```
**特点：**
- 🛡️ 完全禁用CUDA，强制使用CPU
- ✅ 包含PyTorch设备验证
- ✅ 最高的兼容性保证
- ✅ 详细的设备状态检查

**适用场景：**
- 无GPU的服务器环境
- GPU驱动问题时的备用方案
- 故障排除和兼容性测试
- 确保CPU兼容性

## 🔧 环境变量控制

您也可以通过环境变量控制设备选择：

```bash
# 强制使用CPU
export FORCE_CPU=true
python start_server.py

# Windows环境
set FORCE_CPU=true
python start_server.py
```

## 📊 性能对比

| 启动方式 | 检查时间 | 启动速度 | 错误诊断 | 推荐场景 |
|---------|---------|---------|---------|---------|
| `start_server.py` | 完整 | 中等 | 详细 | 生产环境 |
| `app.py` | 无 | 最快 | 基础 | 开发调试 |
| `start_server_cpu_safe.py` | 设备验证 | 中等 | 详细 | CPU专用 |

## 🚨 故障排除

### 如果遇到GPU相关错误：
```bash
python start_server_cpu_safe.py
```

### 如果遇到依赖包问题：
```bash
python start_server.py
# 会显示缺失的依赖包和安装命令
```

### 如果遇到模型文件问题：
```bash
python start_server.py
# 会检查并报告缺失的模型文件
```

## 💡 推荐使用流程

1. **首次运行**：使用 `python start_server.py` 进行完整检查
2. **日常开发**：使用 `python app.py` 快速启动
3. **遇到问题**：使用 `python start_server_cpu_safe.py` 排除GPU问题

## 🌐 服务器信息

无论使用哪种启动方式，服务器都会在以下地址启动：
- 本地访问：http://localhost:5000
- 网络访问：http://your-ip:5000
- 健康检查：http://localhost:5000/health
- 预测接口：http://localhost:5000/predict
