<template>
  <div class="min-h-screen section-padding">
    <div class="container-custom max-w-4xl">
      <!-- Header -->
      <div class="text-center mb-16">
        <h1 class="text-4xl md:text-5xl font-bold text-neutral-900 mb-4">
          Contact Us
        </h1>
        <p class="text-xl text-neutral-600">
          Get in touch with the mLATTICE development team
        </p>
      </div>

      <!-- Contact Information -->
      <div class="max-w-4xl mx-auto">
        <!-- Contact Details -->
        <div class="space-y-12">
          <!-- Contact Cards -->
          <div class="grid md:grid-cols-2 gap-12 max-w-4xl mx-auto mb-20">
            <!-- Research Team -->
            <div class="bg-white rounded-3xl p-12 shadow-sm border border-neutral-100 hover:shadow-lg transition-all duration-500">
              <div class="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-8">
                <UserGroupIcon class="h-10 w-10 text-primary-600" />
              </div>
              <h3 class="text-2xl font-semibold text-neutral-900 mb-4 text-center">
                Research Team
              </h3>
              <div class="text-center space-y-3 text-neutral-600">
                <p class="text-lg font-medium text-neutral-900">Zhang Yanju Bioinformatics Lab</p>
                <p class="text-base text-neutral-600">Committed to promoting research in the field of bioinformatics</p>
              </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white rounded-3xl p-12 shadow-sm border border-neutral-100 hover:shadow-lg transition-all duration-500">
              <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-8">
                <ChatBubbleLeftRightIcon class="h-10 w-10 text-green-600" />
              </div>
              <h3 class="text-2xl font-semibold text-neutral-900 mb-4 text-center">
                Contact Information
              </h3>
              <div class="text-center space-y-3 text-neutral-600">
                <p class="text-base">For any inquiries regarding mLATTICE, please contact us by email. We will be glad to offer assistance</p>
                <div class="pt-6">
                  <a href="mailto:<EMAIL>" class="inline-flex items-center justify-center bg-green-50 hover:bg-green-100 text-green-700 px-6 py-3 rounded-full transition-all duration-300 font-medium">
                    <EnvelopeIcon class="h-5 w-5 mr-2" />
                    <EMAIL>
                  </a>
                </div>
              </div>


            </div>
          </div>

          <!-- Institution Information -->
          <div class="text-center mt-20">
            <div class="max-w-2xl mx-auto bg-white rounded-3xl p-12 shadow-sm border border-neutral-100 hover:shadow-lg transition-all duration-500">
              <div class="w-20 h-20 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-8">
                <BuildingOfficeIcon class="h-10 w-10 text-secondary-600" />
              </div>
              <h3 class="text-2xl font-semibold text-neutral-900 mb-4 text-center">
                Institution
              </h3>
              <div class="text-center space-y-3 text-neutral-600">
                <p class="text-xl font-medium text-neutral-900">HuaQiao University</p>
                <p class="text-lg">College of Computer Science and Technology</p>
                <div class="pt-6 space-y-1 text-neutral-500">
                  <p>Bioinformatics group</p>
                  <p>XiaMen, FuJian, 361021</p>
                </div>

                <div class="pt-8">
                  <div class="flex items-center justify-center">
                    <GlobeAltIcon class="h-5 w-5 mr-3 text-secondary-600" />
                    <a href="https://cst.hqu.edu.cn/" target="_blank" class="text-secondary-600 hover:text-secondary-800 transition-colors">
                      https://cst.hqu.edu.cn/
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>

      <!-- Response Information -->
      <div class="text-center mt-24">
        <div class="max-w-3xl mx-auto bg-neutral-50 rounded-3xl p-16">
          <div class="w-24 h-24 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-8">
            <InformationCircleIcon class="h-12 w-12 text-primary-600" />
          </div>
          <h2 class="text-3xl md:text-4xl font-bold text-neutral-900 mb-6">
            Response Time
          </h2>
          <p class="text-lg text-neutral-600 leading-relaxed max-w-2xl mx-auto">
            We typically respond to inquiries within 24-48 hours during business days.
            For urgent technical issues, please contact our technical support team
            for faster response.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  UserGroupIcon,
  BuildingOfficeIcon,
  ChatBubbleLeftRightIcon,
  EnvelopeIcon,
  GlobeAltIcon,
  InformationCircleIcon
} from '@heroicons/vue/24/outline'
</script>
